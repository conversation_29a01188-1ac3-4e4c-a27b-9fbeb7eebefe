<script setup lang="ts">
  import { TreeNodeData } from 'element-plus'
  import { TreeNodeType } from './types'

  const { enableCheckbox, row, checked, indeterminate, toggleCheckboxEvent } = defineProps<{
    enableCheckbox?: boolean
    row: TreeNodeData
    checked?: boolean
    indeterminate?: boolean
    toggleCheckboxEvent: (row: TreeNodeData) => void
  }>()

  const getNodeOriginData = (row: TreeNodeData) => {
    if (row.nodeType === TreeNodeType.Org) {
      return bfglob.gorgData.get(row.rid)
    } else {
      return bfglob.gdevices.get(row.rid)
    }
  }
</script>

<template>
  <div class="vxe-column-content">
    <span v-if="enableCheckbox" class="vxe-column-checkbox" @click.stop="toggleCheckboxEvent(row)">
      <i v-if="indeterminate" class="bf-iconfont bfdx-fuxuanduoanniu"></i>
      <i v-else-if="checked" class="bf-iconfont bfdx-shuzhuang"></i>
      <i v-else class="bf-iconfont bfdx-dingweimaodianwaikuang"></i>
    </span>
    <el-tooltip
      popper-class="bf-tooltip"
      effect="dark"
      placement="bottom"
      :show-after="1000"
      :content="row.nodeType === TreeNodeType.Org ? getNodeOriginData(row)?.dmrId : getNodeOriginData(row)?.dmrId"
    >
      <span v-if="row.nodeType === TreeNodeType.Org" class="inline-flex justify-start items-center gap-[10px]">
        {{ getNodeOriginData(row)?.orgShortName }}
        <span class="h-[15px] leading-[15px]! px-[5px] bg-[#1a7aff] rounded-[4px] text-[11px] align-middle">
          {{ getNodeOriginData(row)?.sumDeviceCount }}
        </span>
      </span>
      <span v-else>{{ getNodeOriginData(row)?.selfId }}</span>
    </el-tooltip>
  </div>
</template>
