<template>
  <section class="relative w-full h-full overflow-hidden tree-grid" :class="[{ 'has-filter': filter }, { 'with-page-header': withPageHeader }]">
    <template v-if="filter">
      <PageHeader v-if="withPageHeader">
        <template #title>
          <treeFilterInput
            class="translate-x-[26px] translate-y-[-12px] you-she-biao-ti-hei"
            ref="filterInput"
            v-model="filterValue"
            @input="filterOnChange"
            @keydown.enter="filterOnChange"
            show-icon
            :is-in-page-header="withPageHeader"
          />
        </template>
      </PageHeader>
      <TreeFilterInput class="h-[35px]" v-else ref="filterInput" v-model="filterValue" @input="filterOnChange" @keydown.enter="filterOnChange" />
    </template>

    <div class="relative w-full tree-grid-wrapper">
      <div ref="treeGridContainer" class="relative w-full tree-grid-container">
        <vxe-table
          ref="tableRef"
          :show-header="false"
          :show-footer="false"
          show-overflow="ellipsis"
          height="100%"
          border="none"
          :row-config="vxeRowConfig"
          :tree-config="vxeTreeConfig"
          :menu-config="menuConfig"
          @menu-click="menuEventHandler"
          :checkbox-config="{ checkAll: true, reserve: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
        >
          <vxe-column :type="enableCheckbox ? 'checkbox' : null" field="rid" tree-node :filters="vxeFilterOptions" :filter-method="vxeFilterMethod">
            <template v-if="enableCheckbox" #checkbox="{ row, checked, indeterminate }">
              <vxe-column-content
                :enable-checkbox="enableCheckbox"
                :row="row"
                :checked="checked"
                :indeterminate="indeterminate"
                :toggle-checkbox-event="toggleCheckboxEvent"
              />
            </template>
            <template v-else #default="{ row }">
              <vxe-column-content :enable-checkbox="enableCheckbox" :row="row" :toggle-checkbox-event="toggleCheckboxEvent" />
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
  import type { VxeColumnPropTypes, VxeTableInstance, VxeTablePropTypes } from 'vxe-table'
  import { onMounted, reactive, useTemplateRef, ref } from 'vue'
  import { TreeNodeData, TreeNodeType } from './types'
  import { VxeTreeManager } from './vxeTree'
  import { calcScaleSize } from '@/utils/setRem'
  import { MenuEventHandler } from './types'
  import { useResizeObserver } from '@vueuse/core'

  const {
    filter = true,
    withPageHeader = false,
    enableCheckbox = true,
    menuConfig,
    menuEventHandler,
  } = defineProps<{
    filter?: boolean
    withPageHeader?: boolean
    enableCheckbox?: boolean
    menuConfig?: VxeTablePropTypes.MenuConfig
    menuEventHandler?: MenuEventHandler
  }>()

  const vxeRowConfig = ref<VxeTablePropTypes.RowConfig>({ useKey: true, keyField: 'rid', isHover: true, isCurrent: true })
  const vxeTreeConfig = ref<VxeTablePropTypes.TreeConfig>({
    transform: true,
    rowField: 'rid',
    parentField: 'parentOrgId',
    expandAll: true,
    indent: calcScaleSize(20),
    iconClose: 'bf-iconfont bfdx-shouqi',
    iconOpen: 'bf-iconfont bfdx-zhankai',
    showLine: true,
  })

  useResizeObserver(document.body, () => {
    vxeTreeConfig.value.indent = calcScaleSize(20)
  })

  const tableRef = useTemplateRef<VxeTableInstance<TreeNodeData>>('tableRef')
  let treeManager: VxeTreeManager

  const toggleCheckboxEvent = row => {
    tableRef.value?.toggleCheckboxRow(row)
  }

  /** 过滤树结构--开始 */

  const filterValue = ref('')
  const vxeFilterOptions = reactive<VxeColumnPropTypes.Filters<string>>([{ data: '' }])

  const vxeFilterMethod: VxeColumnPropTypes.FilterMethod<TreeNodeData> = ({ option, row }) => {
    if (row.nodeType == TreeNodeType.Org) {
      const data = bfglob.gorgData.get(row.rid)
      return data?.orgShortName.indexOf(option.data) > -1
    } else {
      const data = bfglob.gdevices.get(row.rid)
      return data?.selfId.indexOf(option.data) > -1
    }
  }

  const filterOnChange = (): void => {
    if (filterValue.value) {
      tableRef.value?.setFilter('rid', [{ data: filterValue.value, checked: true }], true)
    } else {
      tableRef.value?.clearFilter()
      treeManager.reload()
    }
  }

  /** 过滤树结构--结束 */
  onMounted(() => {
    treeManager = new VxeTreeManager(tableRef.value)
  })
</script>

<style lang="scss">
  @use '@/assets/bfdxFont/iconfont.css';

  .tree-grid {
    --vxe-ui-font-family: 'AlibabaPuHuiTi2';
    --vxe-ui-font-primary-color: #7e8fa6;
    --vxe-ui-font-color: #7e8fa6;
    --vxe-ui-font-size-default: 12px;
    --vxe-ui-table-row-height-default: 36px;
    --vxe-ui-table-row-line-height: 20px;

    &.has-filter .tree-grid-wrapper {
      height: calc(100% - 44px);
      margin-top: 7px;
    }

    .tree-grid-wrapper {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.22), transparent);
      border-width: 1px;
      border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;
      height: 100%;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 4, 0.27);
        pointer-events: none;
        filter: blur(200px);
      }
    }
    .tree-grid-container {
      height: 100%;
    }
  }
</style>
