import { watch } from 'vue'
import { VxeTableInstance } from 'vxe-table/types/all'
import { cloneDeep } from 'lodash'
import { HasPrivilegeDataLoaded } from '@/utils/bfprocess'
import { TreeNodeData, TreeNodeType } from './types'
import { updateTreeOrgDeviceCount } from './treeUtil'

// 自定义 fancytree 节点排序比较方法
const fullCallDmrIdStr = bfglob.fullCallDmrId.toString()

export function defaultVxeTableTreeSortCompareFn(a: TreeNodeData, b: TreeNodeData): number {
  // 全呼目标不参与排序
  const aData = a.nodeType == TreeNodeType.Org ? bfglob.gorgData.get(a.rid) : bfglob.gdevices.get(a.rid)
  const bData = b.nodeType == TreeNodeType.Org ? bfglob.gorgData.get(b.rid) : bfglob.gdevices.get(b.rid)

  if (aData.dmrId === fullCallDmrIdStr || bData.dmrId === fullCallDmrIdStr) {
    return 0
  }
  const sortType = bfglob.userInfo.setting.fancytreeSortType

  // 将单位节点排在设备节点后面,sortType 为 false 则将单位和设备排序反转
  if (a.nodeType === TreeNodeType.Org && b.nodeType !== TreeNodeType.Org) {
    return sortType ? 1 : -1
  }
  if (a.nodeType !== TreeNodeType.Org && b.nodeType === TreeNodeType.Org) {
    return sortType ? -1 : 1
  }

  // 单位节点排序，必须按排序值排序
  if (a.nodeType === TreeNodeType.Org && b.nodeType === TreeNodeType.Org) {
    return aData.sortString - bData.sortString
  }

  return aData.dmrId.localeCompare(bData.dmrId)
}

let GlobalVxeTreeManager: VxeTreeManager | null = null
export function getGlobalVxeTreeManager(): Readonly<VxeTreeManager | null> {
  return GlobalVxeTreeManager
}
export function setGlobalVxeTreeManager(tree: VxeTreeManager): void {
  if (!GlobalVxeTreeManager) {
    GlobalVxeTreeManager = tree
  }
}

type VxeTreeManagerOptions = {
  sortMethod?: (a: TreeNodeData, b: TreeNodeData) => number
}

function loadTreeDataFromBfglob() {
  const orgData = bfglob.gorgData?.getAll()
  const deviceData = bfglob.gdevices?.getAll()

  const allTreeData: TreeNodeData[] = []

  for (const key in orgData) {
    const node: TreeNodeData = {
      rid: orgData[key].rid,
      parentOrgId: orgData[key].parentOrgId,
      nodeType: TreeNodeType.Org,
    }
    allTreeData.push(node)
  }

  for (const key in deviceData) {
    const node: TreeNodeData = {
      rid: deviceData[key].rid,
      parentOrgId: deviceData[key].orgId,
      nodeType: TreeNodeType.Terminal,
    }
    allTreeData.push(node)
  }

  return allTreeData
}

// 从 bfglob 中加载树形数据，如果blglob没有初始化成功，等待并重试
export async function loadTreeData(): Promise<TreeNodeData[]> {
  return await new Promise(resolve => {
    const interval = setInterval(() => {
      if (HasPrivilegeDataLoaded()) {
        clearInterval(interval)
        resolve(loadTreeDataFromBfglob())
      }
    }, 100)
  })
}

export const syncAddOrgNode = <T extends { rid: string; parentOrgId: string }>(orgItem: T) => {
  updateTreeOrgDeviceCount()

  const treeData = {
    rid: orgItem.rid,
    parentOrgId: orgItem.parentOrgId,
    nodeType: TreeNodeType.Org,
  }
  setTimeout(() => {
    bfglob.emit('treeInsert', treeData)
  }, 0)
}

export const syncUpdateOneOrgNode = <T extends { rid: string; parentOrgId: string }>(orgItem: T) => {
  updateTreeOrgDeviceCount()

  const treeData = {
    rid: orgItem.rid,
    parentOrgId: orgItem.parentOrgId,
    nodeType: TreeNodeType.Org,
  }
  setTimeout(() => {
    bfglob.emit('treeUpdate', treeData)
  }, 0)
}

export const syncRemoveOneOrgNode = (orgItemRid: string) => {
  updateTreeOrgDeviceCount()

  const treeData = {
    rid: orgItemRid,
    nodeType: TreeNodeType.Org,
  }
  setTimeout(() => {
    bfglob.emit('treeRemove', treeData)
  }, 0)
}

export const syncAddOneDeviceNode = <T extends { rid: string; orgId: string }>(deviceItem: T) => {
  updateTreeOrgDeviceCount()

  const treeData = {
    rid: deviceItem.rid,
    parentOrgId: deviceItem.orgId,
    nodeType: TreeNodeType.Terminal,
  }
  setTimeout(() => {
    bfglob.emit('treeInsert', treeData)
  }, 0)
}

export const syncUpdateOneDeviceNode = <T extends { rid: string; orgId: string }>(deviceItem: T) => {
  updateTreeOrgDeviceCount()

  const treeData = {
    rid: deviceItem.rid,
    parentOrgId: deviceItem.orgId,
    nodeType: TreeNodeType.Terminal,
  }
  setTimeout(() => {
    bfglob.emit('treeUpdate', treeData)
  }, 0)
}

export const syncRemoveOneDeviceNode = (deviceItemRid: string) => {
  updateTreeOrgDeviceCount()

  const treeData = {
    rid: deviceItemRid,
    nodeType: TreeNodeType.Terminal,
  }
  setTimeout(() => {
    bfglob.emit('treeRemove', treeData)
  }, 0)
}

export class VxeTreeManager {
  treeRef: VxeTableInstance<TreeNodeData> | null = null
  options: VxeTreeManagerOptions = {}
  constructor(treeRef: VxeTableInstance<TreeNodeData> | null, options: VxeTreeManagerOptions = {}) {
    this.treeRef = treeRef
    this.options = {
      sortMethod: defaultVxeTableTreeSortCompareFn,
      ...options,
    }

    if (!GlobalVxeTreeManager) {
      setGlobalVxeTreeManager(this)

      loadTreeData().then(data => {
        this.initTreeData(data)
      })
    }

    bfglob.on('treeInsert', this.handleInsert)
    bfglob.on('treeRemove', this.handleRemove)
    bfglob.on('treeUpdate', this.handleUpdate)
    bfglob.on('treeSortChildren', this.sort)
    watch(
      () => treeRef,
      value => {
        if (!value) {
          bfglob.off('treeInsert', this.handleInsert)
          bfglob.off('treeRemove', this.handleRemove)
          bfglob.off('treeUpdate', this.handleUpdate)
          bfglob.off('treeSortChildren', this.sort)
        }
      }
    )
  }

  sort() {
    const data = this.treeRef.getData()
    data.sort(this.options.sortMethod)
    this.treeRef?.loadData(data)
  }

  reload() {
    this.treeRef?.updateData()
  }

  initTreeData(data: TreeNodeData[]) {
    data.sort(this.options.sortMethod)
    this.treeRef?.reloadData(data)
  }

  handleInsert(node: TreeNodeData) {
    const parentRow = this.treeRef.getRowById(node.parentOrgId)
    if (parentRow) {
      this.treeRef?.insertChild(node, parentRow)
    } else {
      this.treeRef?.insert(node)
    }
  }
  handleRemove(node: TreeNodeData) {
    // 传入的node没有parentOrgId，尝试使用rid获取row
    const rawNode = this.treeRef?.getRowById(node.rid)
    this.treeRef?.remove(rawNode)
  }
  handleUpdate(node: TreeNodeData) {
    const rawNode = this.treeRef?.getRowById(node.rid)
    if (!rawNode) {
      return
    }
    this.treeRef?.setRow(rawNode, rawNode)
  }

  cloneTree(filter: (node: TreeNodeData) => boolean): TreeNodeData[] {
    return cloneDeep(this.treeRef.getData()).filter(filter)
  }
}
