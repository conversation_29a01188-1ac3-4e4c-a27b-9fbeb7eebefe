<template>
  <bf-dialog
    v-model="visible"
    ref="bfSpeaking"
    :title="$t('dialog.networkSpeaking')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    class="header-border shadow-md shadow-slate-800 speaking-dialog drag-dialog"
    modal-class="drag-dialog-modal"
    width="420px"
    append-to-body
    @open="openDlgFn"
    draggable
    center
  >
    <el-form ref="speakInfo" v-model="speakInfo" label-position="left" :label-width="speakInfoFormLabelWidth">
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.maxSpeakTime') + '：'" :style="{ height: '50px', lineHeight: '50px' }" />
        </template>
        <bf-input-numberV2 v-model="speakInfo.maxSpeakTime" :min="30" :max="300" :step="10" class="max-speak-time" />
      </el-form-item>
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.speakDevice') + '：'" :style="{ height: '50px', lineHeight: '50px' }" />
        </template>
        <bf-input v-model="speakDevice" readonly />
      </el-form-item>
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.callTarget') + '：'" :style="{ height: '50px', lineHeight: '50px' }" />
        </template>
        <bf-input v-model="targetName" readonly class="send-group-append">
          <template #prepend>
            <el-popover
              ref="dynamicGroupMembers"
              popper-class="listenGroupPopover bf-tooltip"
              placement="left"
              :title="$t('dynamicGroup.dynamicGroupMembers')"
              width="200"
              trigger="click"
            >
              <div class="listenGroupList">
                <div v-for="(memberName, idx) in dynamicGroupMemberInfo" :key="memberName" class="listenGroupList-item">
                  {{ `${idx + 1}: ${memberName}` }}
                </div>
              </div>
              <template #reference>
                <el-button class="target-input-btn" :disabled="disableDynamicGroupMember">
                  <span class="bf-iconfont bfdx-tongzhixiaoxi text-[white]"></span>
                </el-button>
              </template>
            </el-popover>
          </template>
          <template #append>
            <el-button class="target-input-btn" @click="createAndFastSpeak">
              <span class="bf-iconfont bfdx-Subtract text-[white]"></span>
            </el-button>
            <el-button class="target-input-btn" @click="selectSpeakTarget">
              <span class="bf-iconfont bfdx-tianjia222 text-[white]"></span>
            </el-button>
          </template>
        </bf-input>
      </el-form-item>
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.listenGroup') + '：'" :style="{ height: '50px', lineHeight: '50px' }" />
        </template>
        <bf-input v-model="listenGroupName" readonly class="send-group-append">
          <template #prepend>
            <el-popover
              ref="listenGroupPopover"
              popper-class="listenGroupPopover bf-tooltip"
              placement="left"
              :title="$t('dialog.listenGroup')"
              width="200"
              trigger="click"
            >
              <div class="listenGroupList">
                <div v-for="(groupName, idx) in listenGroupInfo" :key="idx" class="listenGroupList-item">
                  {{ `${idx + 1}: ${groupName}` }}
                </div>
              </div>
              <template #reference>
                <el-button class="target-input-btn" :disabled="disableDynamicGroupMember">
                  <span class="bf-iconfont bfdx-tongzhixiaoxi text-[white]"></span>
                </el-button>
              </template>
            </el-popover>
          </template>
          <template #append>
            <el-button icon="more" @click="selectListenGroup" />
          </template>
        </bf-input>
      </el-form-item>
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.callBackTarget') + '：'" :style="{ height: '50px', lineHeight: '50px' }" />
        </template>
        <bf-input v-model="callbackTargetName" readonly />
      </el-form-item>
      <el-form-item v-if="phoneNo" class="incoming-call">
        <template #label>
          <EllipsisText :content="$t('dialog.incomingCall') + '：'" :style="{ height: '50px', lineHeight: '50px' }" />
        </template>
        <bf-input v-model="phoneNo" class="incoming-call-phoneNo" />
        <el-button-group class="incoming-call-btns">
          <el-tooltip popper-class="bf-tooltip" effect="dark" :content="$t('dialog.hangup')" placement="bottom">
            <el-button type="danger" class="iconfont icon-hangup" @click="telephoneHangup" />
          </el-tooltip>
          <el-tooltip popper-class="bf-tooltip" effect="dark" :content="$t('dialog.callTransfer')" placement="bottom">
            <el-button type="primary" class="iconfont icon-phone-transfer" @click="initTransferTargetTree" />
          </el-tooltip>
        </el-button-group>
      </el-form-item>
      <el-form-item>
        <template #label>
          <el-button :disabled="disUsbTip" class="bf-form-item-button !h-[50px] w-full">
            <p class="usb-icon"><span class="bf-iconfont bfdx-tubiao23 text-[#8299A9]"></span></p>
            <span class="usb-name" v-text="disUsbTip ? '' : usbDeviceName" />
          </el-button>
        </template>
        <el-button icon="circle-close" class="bf-form-item-button !h-[50px] w-full" :disabled="!pendingTarget" @click.prevent="cancelPending">
          <p class="cancel-hang-icon"><span class="bf-iconfont bfdx-tongzhixiaoxi text-[#8299A9]"></span></p>
          <span>{{ $t('dialog.cancelPending') }}</span>
        </el-button>
      </el-form-item>
      <el-form-item label-width="0" class="center speak-microphone">
        <div class="animate-speak-box">
          <el-tooltip popper-class="bf-tooltip" :disabled="canSpeaking" :content="$t('msgbox.firstConnectTC918')" placement="bottom">
            <el-button ref="speakBtn" class="animate-speak-btn" :type="speakBtnType" :disabled="!canSpeaking" @click.prevent="toggleSpeaking">
              <!-- <img v-for="(item, index) in speakImages" v-show="index == speakImagesIndex" :key="index" :src="item" class="animate-speak-image" /> -->
              <img src="@/assets/images/dispatch/function_list/broadcast_call.svg"></img>
            </el-button>
          </el-tooltip>
        </div>
        <span class="speak-prompt" v-text="$t('dialog.speakPrompt')" />
      </el-form-item>
    </el-form>

    <!-- 选择通话目标对话框 -->
    <el-dialog
      v-model="showTargetTree"
      :title="groupTreeTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :fullscreen="fullscreen"
      append-to-body
      width="420px"
      class="header-border speak-target-dialog"
      @close="groupTreeConfirmFunc"
    >
      <TableTree
        :ref="targetTreeId"
        :treeId="targetTreeId"
        :contextmenuOption="contextmenuOption"
        :option="targetTreeOption"
        :filterOption="filterOption"
        in-dialog
        @select="selectNodes"
        @click="clickNode"
        @loaded="targetTreeLoaded"
      />
    </el-dialog>

    <!-- 选择收听组对话框 -->
    <el-dialog
      v-model="showListGroupTree"
      :title="groupTreeTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :fullscreen="fullscreen"
      append-to-body
      width="420px"
      class="header-border listen-group-dialog"
    >
      <TableTree
        :ref="sendGroupTreeId"
        :treeId="sendGroupTreeId"
        :option="treeOption"
        :filterOption="filterOption"
        @select="selectNodes"
        @click="clickNode"
        @loaded="listenGroupTreeLoaded"
      />
    </el-dialog>
  </bf-dialog>
</template>

<script>
  import { defineAsyncComponent, h } from 'vue'
  import { DbOrgIsVirtual, DynamicGroupState } from '@/utils/dynamicGroup/api'
  import { cloneDeep, debounce } from 'lodash'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfProcess from '@/utils/bfprocess'
  import bfTree, { addDynamicGroupNode, defaultTreeId, isRootNode, removeNode, updateOneOrgNodeTitle } from '@/utils/bftree'
  import bfutil, { deferred, getDynamicGroupOrgType } from '@/utils/bfutil'
  import bfCrypto from '@/utils/crypto'
  import dynamicGroupTree from '@/utils/dynamicGroup/dynamicGroupMixin'
  import bfNotify from '@/utils/notify'
  import qWebChannelObj from '@/utils/qWebChannelObj'
  import { v1 as uuid } from 'uuid'
  import vueMixin from '@/utils/vueMixin'
  import { ProxyServer } from '@/envConfig'
  import eventBus from '@/utils/eventBus'
  import { useRouteParams } from '@/router'
  import { ElButton } from 'element-plus'
  import bfDialog from '@/components/bfDialog/main'
  import bfInputNumberV2 from '@/components/bfInputNumber/main'
  import bfInput from '@/components/bfInput/main'
  import EllipsisText from '@/components/common/EllipsisText.vue'

  const listenGroupTreeNodeOption = {
    showCounter: false,
  }
  const dynamicGroupTypes = getDynamicGroupOrgType()

  const recordIconCtx = import.meta.glob(['@/images/network/record_icon_*.png'], {
    eager: true,
  })
  const { setRouteParams } = useRouteParams()

  export default {
    name: 'BfSpeaking',
    components: {
      bfInput,
      EllipsisText,
      TableTree: defineAsyncComponent(() => import('@/components/common/tableTree')),
      bfDialog,
      bfInputNumberV2,
    },
    mixins: [dynamicGroupTree, vueMixin],
    data() {
      const targetTreeId = 'voipTargetTree'
      const sendGroupTreeId = 'sendGroupTree'

      return {
        visible: false,

        // 通话的信息配置，发射组、目标等
        speakInfo: {
          speaker: '',
          target: '',
          maxSpeakTime: 60,
          priority: 3,
          listenGroup: [],
        },
        voipServerInfo: null,
        voipServerConnected: false,
        pendingTarget: '',
        pendingTime: 5000,
        pendingSsid: '',

        targetTreeId,
        showTargetTree: false,
        targetTreeReady: deferred(),
        sendGroupTreeId,
        showListGroupTree: false,
        listenGroupTreeReady: deferred(),
        // selectNodeTarget：1为通话目标，2为接收组，3为电话转接
        selectNodeTarget: 0,
        hasUsbDevice: false,
        usbDeviceName: '',
        connectCount: 0,

        // 动画
        speakImages: [...Object.values(recordIconCtx).map(item => item.default)],
        speakImagesIndex: 0,
        speakBtnType: 'primary',
        // 讲话状态，0 结束讲话，1 自己在讲话，2 他人在讲话
        speakState: 0,
        otherSpeaking: false,
        // 当前是谁在讲话
        currentSpeaker: '',

        setKeysEvent: false,
        // 跨组组呼的临时收听组dmrId
        tempListenGroup: '',

        // 检测录音和放音定时器
        localPlayingTimer: null,
        localRecordingTimer: null,
        checkSpeakingInterval: 2500,

        // 电话呼叫及呼叫转移
        phoneNo: '',
        transferTargetTemp: '',
        transferTarget: '',
        phoneTransferBc15: {},
      }
    },
    methods: {
      // 设备树或动态组添加成功后快速启动联网通话，并尝试发起通话
      async speakFast(target) {
        // 无通话目标
        if (!target) {
          return
        }

        // 通话目标不应该为自己
        if (target === this.speakInfo.speaker) {
          bfNotify.messageBox(this.$t('msgbox.targetNotSelf'), 'warning')
          return
        }

        const awaitVoipRegister = () => {
          return new Promise(resolve => {
            if (this.voipServerConnected) {
              return resolve()
            }

            // 未初始化TC918，订阅连接事件
            eventBus.once('sg_voip_register_ok', () => {
              this.$nextTick(resolve)
            })
          })
        }
        await awaitVoipRegister()

        this.speakInfo.target = target

        // 在下一事件循环处理，以便通话目标可以正确设置
        setTimeout(() => {
          // 不能进行通话，可能没有指挥坐席等
          if (!this.canSpeaking) {
            return
          }
          // 讲话状态，0 结束讲话，1 自己在讲话，2 他人在讲话
          // 可以抢话语权
          if (this.speakState === 1) {
            // 开始通话前要先结束当前通话
            this.sl_i_speak_end()
          }

          this.sl_i_speak_start()
        }, 0)
      },
      createAndFastSpeak() {
        // 先解绑上一次的订阅事件
        if (this.lastCreateFastDynamicGroupSubject) {
          bfglob.off(this.lastCreateFastDynamicGroupSubject)
        }
        const subject = (this.lastCreateFastDynamicGroupSubject = 'create-dynamic-group-' + Date.now())
        bfglob.once(subject, target => {
          this.speakInfo.target = target
          this.lastCreateFastDynamicGroupSubject = undefined
        })
        const params = {
          fastCreateDynamicGroup: true,
          routeName: this.$route.name,
          reply: subject,
        }
        // 路由跳转到动态组页面，以创建动态组
        // 判断当前路由是否为动态组，不能跳转到当前路由,会报"NavigationDuplicated"错误
        if (this.$route.name === 'dynamicGroup') {
          // 已经是动态组页面,直接发布事件
          bfglob.emit('create-fast-temp-group', params)
        } else {
          setRouteParams('dynamicGroup', params)
          this.$router.replace({ name: 'dynamicGroup' })
        }
      },
      targetTreeLoaded() {
        this.loadSpeakerTargetNodes()
          .then(() => {
            this.targetTreeReady.resolve(true)
          })
          .catch(() => {})
      },
      listenGroupTreeLoaded() {
        const afterLoaded = () => {
          this.disableExpiredDynamicGroup(this.sendGroupTreeId)
          this.keepSelectNode()
          this.listenGroupTreeReady.resolve(true)
        }
        const orgList = bfutil.objToArray(bfglob.gorgData.getAll())
        const source = bfTree.createTreeOrgSource(orgList, {
          ...listenGroupTreeNodeOption,
          selected: false,
        })
        bfTree.addOrgNodeToTree(this.sendGroupTreeId, source)
        // 确保树节点已经生成再处理后续逻辑
        this.$nextTick(afterLoaded)
      },
      selectNodes(event, data) {
        const selectKeys = $.map(data.tree.getSelectedNodes(), function (node) {
          return node.key
        })
        this.setSpeakInfo(selectKeys)
      },
      clickNode(event, data) {
        const excludeList = ['expander', 'prefix', 'checkbox']
        if (excludeList.includes(data.targetType)) {
          return true
        }
        const node = data.node
        if (!node) {
          return false
        }
        node.setActive()
        node.setSelected(!node.isSelected())
      },
      initVoipServer() {
        if (this.voipServer) {
          this.initServerSuccess()
        } else {
          this.voipServerConnected = false
          // 停止当前的通话状态
          this.endSpeakStyle()
          this.setUsbStatus('')
          this.pendingTarget = ''
        }
      },
      initKeysEvent() {
        this.$nextTick(() => {
          if (this.$el.firstElementChild) {
            this.setKeysEvent = true
            let isKeyDown = false
            document.onkeydown = function (e) {
              // bfglob.console.log("onkeydown", e.keyCode, this);
              if (isKeyDown) {
                return
              }
              isKeyDown = true
              switch (e.keyCode) {
                case 86:
                  // V
                  if (!e.ctrlKey) {
                    return
                  }
                  bfglob.emit('onkeydownStartSpeak')
                  break
                case 113:
                  // F2
                  bfglob.emit('onkeydownStartSpeak')
                  break
                // 如果是空格或回车，则阻止默认事件
                case 32:
                  //e.preventDefault()
                  if (document.activeElement.nodeName === 'INPUT') {
                    return
                  }
                  bfglob.emit('onkeydownStartSpeak')
              }
            }
            document.onkeyup = function (e) {
              // bfglob.console.log("onkeyup", e.keyCode, this);
              isKeyDown = false
              switch (e.keyCode) {
                case 86:
                  // V
                  if (!e.ctrlKey) {
                    return
                  }
                  bfglob.emit('onkeyupEndSpeak')
                  break
                case 113:
                  // F2
                  bfglob.emit('onkeyupEndSpeak')
                  break
                // 如果是空格或回车，则阻止默认事件
                case 13:
                case 32:
                  if (document.activeElement.nodeName === 'INPUT') {
                    return
                  }
                  bfglob.emit('onkeyupEndSpeak')
              }
            }
          }
        })
      },
      openDlgFn() {
        this.initSpeakInfo()
        qWebChannelObj.initServer()
      },
      groupTreeConfirmFunc() {
        switch (this.selectNodeTarget) {
          case 1:
            // 通话目标
            break
          case 2:
            // 接收组
            break
          case 3:
            // 电话转接
            this.transferTarget = this.transferTargetTemp
            this.transferTargetTemp = ''

            // 发送电话转接指令
            this.slPhoneTransfer()
            break
        }
      },
      keepSelectNode() {
        let target = []
        let treeId = this.targetTreeId
        switch (this.selectNodeTarget) {
          case 1:
            // 通话目标
            const key = this.speakInfo.target

            // 判断是否为全呼
            if (key === bfglob.fullCallDmrId) {
              target.push(key)
              break
            }

            const orgItem = bfglob.gorgData.getDataByIndex(key)
            if (orgItem) {
              target.push(orgItem.rid)
            } else {
              const device = bfglob.gdevices.getDataByIndex(key)
              if (device) {
                target.push(device.rid)
              }
            }
            break
          case 2:
            // 接收组
            treeId = this.sendGroupTreeId
            target = this.speakInfo.listenGroup.map(key => {
              const orgItem = bfglob.gorgData.getDataByIndex(key)
              if (orgItem) {
                return orgItem.rid
              }
            })
            break
          case 3:
            // 电话转接
            const transferTargetOrg = bfglob.gorgData.getDataByIndex(this.transferTarget)
            if (transferTargetOrg) {
              target.push(transferTargetOrg.rid)
            } else {
              const transferTargetDevice = bfglob.gdevices.getDataByIndex(this.transferTarget)
              if (transferTargetDevice) {
                target.push(transferTargetDevice.rid)
              }
            }
            break
        }

        // 清空过滤结果
        this.$refs[treeId]?.clearFilter()
        this.$refs[treeId]?.selectAll(false)

        let firstSelectNode
        for (let i = 0; i < target.length; i++) {
          const rid = target[i]
          const treeNode = bfTree.getTreeNodeByRid(treeId, rid)
          if (treeNode) {
            treeNode.setSelected(true)
            if (!firstSelectNode) {
              firstSelectNode = treeNode
            }
          }
        }

        if (firstSelectNode) {
          // 如果已经聚焦，则先失去焦点，否则无法跳转到节点
          if (firstSelectNode.isActive()) {
            firstSelectNode.setActive(false)
          }
          firstSelectNode.setActive(true)
          firstSelectNode.setFocus()
        }
      },
      disableSelfTarget() {
        const speakerDevice = bfglob.gdevices.getDataByIndex(this.speakInfo.speaker)
        if (!speakerDevice) {
          return
        }
        this.$refs[this.targetTreeId]?.setNodeSelected(speakerDevice.rid, false)
        this.$refs[this.targetTreeId]?.unselectableNodeByKey(speakerDevice.rid, true)
      },
      disableFullCall() {
        this.$refs[this.targetTreeId]?.unselectableNodeByKey(bfglob.fullCallDmrId, !bfglob.userInfo.setting.fullCallPerm)
      },
      disableExpiredDynamicGroup(treeId) {
        bfglob.gorgData
          .getDynamicGroup()
          .filter(item => item.orgIsVirtual === DbOrgIsVirtual.TaskGroup && item.dynamicGroupState === DynamicGroupState.Expired)
          .forEach(item => {
            this.$refs[treeId]?.setNodeSelected(item.rid, false)
            this.$refs[treeId]?.unselectableNodeByKey(item.rid, true)
          })
      },
      disableSpecifiedNodes() {
        // 如果没有全呼权限，则禁止选择全呼目标
        this.disableFullCall()
        // 通话目标不能选择自己
        this.disableSelfTarget()
        // 禁用失效的任务组
        this.disableExpiredDynamicGroup(this.targetTreeId)
      },
      async loadSpeakerTargetNodes() {
        await this.$refs[this.targetTreeId]?.toDictTree(defaultTreeId, dict => {
          const extraData = dict.data || {}
          // 如果是虚拟单位下的节点，则过滤掉
          if (!extraData.isOrg && extraData.virtual) {
            return false
          }
          // 过滤掉动态组下成员
          return isRootNode(dict) || !extraData.isDynamicGroupMember
        })

        // 添加一个全呼通讯录节点
        bfTree.addFullCallNode(this.targetTreeId)
        this.disableSpecifiedNodes()
      },
      initTransferTargetTree() {
        this.selectNodeTarget = 3
        this.showTargetTree = true
        this.targetTreeReady.then(() => {
          if (this.selectNodeTarget !== 3) return
          this.keepSelectNode()
        })
      },
      selectSpeakTarget() {
        this.selectNodeTarget = 1
        this.showTargetTree = true
        this.targetTreeReady.then(() => {
          if (this.selectNodeTarget !== 1) return
          this.keepSelectNode()
        })
      },
      selectListenGroup() {
        this.selectNodeTarget = 2
        this.showListGroupTree = true
      },
      setSpeakTarget(selectKeys) {
        const key = selectKeys.shift() || ''
        // 先判断是否为全呼
        if (key === bfglob.fullCallDmrId) {
          this.speakInfo.target = key
          return
        }

        const orgItem = bfglob.gorgData.get(key)
        if (orgItem) {
          this.speakInfo.target = orgItem.dmrId || ''
        } else {
          const device = bfglob.gdevices.get(key)
          if (device) {
            this.speakInfo.target = device.dmrId || ''
          } else {
            this.speakInfo.target = ''
          }
        }
      },
      setSpeakListenGroup(selectKeys) {
        const listenGroup = []
        selectKeys.forEach(key => {
          const orgItem = bfglob.gorgData.get(key)
          if (orgItem && orgItem.dmrId) {
            listenGroup.push(orgItem.dmrId)
          }
        })
        this.speakInfo.listenGroup = listenGroup
      },
      setSpeakInfo(selectKeys) {
        switch (this.selectNodeTarget) {
          case 1:
            // 设置通话目标
            this.setSpeakTarget(selectKeys)
            break
          case 2:
            // 设置收听组
            this.setSpeakListenGroup(selectKeys)
            break
          case 3:
            // 设置电话转接目标
            this.setTransferTarget(selectKeys)
            break
        }
      },
      initSpeakInfo() {
        const info = bfglob.userInfo.setting.voipSpeakInfo
        let data

        // 如果没有设置通话目标，则以用户所在组为通话目标
        if (!info.target || !(bfglob.gorgData.getDataByIndex(info.target) || bfglob.gdevices.getDataByIndex(info.target))) {
          data = bfglob.gorgData.get(bfglob.userInfo.orgId)
          info.target = data?.dmrId || ''
        }
        // 如果没有设置接收组，则接收用户所在组的语音
        if (!info.listenGroup || info.listenGroup.length === 0) {
          data = data || bfglob.gorgData.get(bfglob.userInfo.orgId)
          info.listenGroup = data ? [data.dmrId] : []
        } else {
          // 需要过滤已经不存在的dmrId数据 //todo need add noPermission unit
          info.listenGroup = info.listenGroup.filter(item => !!bfglob.gorgData.getDataByIndex(item))
        }

        // 合并参数
        this.speakInfo = Object.assign({}, this.speakInfo, info)
      },
      initKeyBoardEvents() {
        // 系统电脑讲话快捷键功能操作
        bfglob.on('onkeydownStartSpeak', () => {
          clearTimeout(this.endSpeakTimes)
          // 如果正在通话中或者麦克风按钮被禁或者是其他人员在讲话，则停止开始讲话指令
          if (!this.canSpeaking || this.speakState !== 0) {
            return
          }
          this.sl_i_speak_start()
        })
        bfglob.on('onkeyupEndSpeak', () => {
          // 如果已结束通话，则停止指令
          if (!this.canSpeaking || this.speakState !== 1) {
            return
          }
          // 设置timeout定时器防止抖动误操作结束讲话
          this.endSpeakTimes = setTimeout(() => {
            this.sl_i_speak_end()
          }, 300)
        })
      },

      init() {
        // this.getVoipServerInfo();
        // 先设置默认参数
        this.initSpeakInfo()
        this.initVoipServer()
        this.initKeyBoardEvents()
      },
      initServerSuccess() {
        // 设置参数
        this.sl_setupVoipHost()
        this.sl_setupUserInfo()
        this.sl_setupTargetDmrid()
        this.sl_updateListenGroup()
        this.sl_setMaxSpeakingTime()

        // 监听 voipServer 信号
        this.listenVoipServerSingle()

        // 客户端连接服务器
        this.sl_connect_server()
        this.sl_getUsb3000Name()
      },
      sl_setupVoipHost() {
        if (!this.voipServer) {
          return
        }

        const hostname = ProxyServer.hostname
        const port = ProxyServer.webPort
        this.voipServer.sl_setupVoipHost(hostname, port)
      },
      sl_setupUserInfo() {
        if (!this.voipServer) {
          return
        }
        this.voipServer.sl_setupUserInfo(this.speakInfo.speaker, this.speakInfo.priority)
      },
      sl_setupTargetDmrid() {
        if (!this.voipServer) {
          return
        }
        this.voipServer.sl_setupTargetDmrid(this.setupTarget)
      },
      sl_updateListenGroup(_listenGroup = this.speakInfo.listenGroup) {
        const exLsGroup = Array.from(new Set([...this.cmdAgentExLisGroup, ...this.speakInfo.listenGroup]))
        if (!this.voipServer) {
          return
        }
        this.voipServer.sl_updateListenGroup(exLsGroup.join(','))
      },
      sl_setMaxSpeakingTime() {
        if (!this.voipServer || !this.speakInfo.maxSpeakTime) {
          return
        }
        this.voipServer.sl_setMaxSpeakingTime(this.speakInfo.maxSpeakTime)
      },
      sl_connect_server() {
        if (!this.voipServer) {
          return
        }
        this.voipServer.sl_connect_server()
      },
      setUsbStatus(name) {
        this.hasUsbDevice = !!name
        this.usbDeviceName = name
      },
      sl_getUsb3000Name() {
        if (!this.voipServer) {
          return
        }
        this.voipServer.sl_getUsb3000Name(name => {
          this.setUsbStatus(name)
        })
      },
      sl_i_speak_start() {
        if (!this.voipServer) {
          return
        }
        // lastTarget = this.speakInfo.target
        this.voipServer.sl_i_speak_start(this.speakInfo.speaker, this.setupTarget)
      },
      sl_i_speak_end() {
        if (!this.voipServer) {
          return
        }
        this.voipServer.sl_i_speak_end(this.speakInfo.speaker)
      },
      sg_local_speaking_end() {
        this.setEndSpeakState()
        // 清除检测本地讲话定时器
        clearInterval(this.localRecordingTimer)

        this.checkCallSessionShouldExpired()
      },
      setPendingTarget(speaker_dmr_id, speaker_target_dmr_id) {
        // 单呼自己，回呼目标需要设置为源目标
        if (this.speakInfo.speaker === speaker_target_dmr_id) {
          this.pendingTarget = speaker_dmr_id
        } else {
          this.pendingTarget = speaker_target_dmr_id
        }
      },
      checkCallSessionShouldExpired(timeout) {
        const ssid = uuid()
        this.pendingSsid = ssid

        // 5秒挂起时间内，如果没有回呼对方，则结束挂起会话
        setTimeout(() => {
          // 讲话状态不为0,则有人在讲话，即回呼或继续呼叫
          if (this.speakState !== 0) {
            return
          }
          if (this.pendingSsid !== ssid) {
            return
          }

          // 会话id相同，则没有人回呼对方，结束挂起
          this.resumeTarget()
          this.setPhoneNo()
        }, timeout || this.pendingTime)
      },
      listenVoipServerSingle() {
        if (!this.voipServer) {
          return
        }

        this.voipServer.sg_voip_register_ok.connect(() => {
          bfglob.console.log('sg_voip_register_ok')
          bfNotify.messageBox(this.$t('msgbox.UsbServerConnectSuccess'), 'success')
          this.voipServerConnected = true
          eventBus.emit('sg_voip_register_ok')
        })
        this.voipServer.sg_info.connect(_info => {})

        // 接收本机讲话结果消息
        this.voipServer.sg_want_to_speak_result.connect(msg => {
          bfglob.console.log('sg_want_to_speak_result', msg, this.otherSpeaking)
          // 应答结果
          // 0x00：时隙占有，准备接收语音呼叫
          // 0x01：允许联网呼叫
          // 0x02：允许呼叫，中继与服务器断开
          // 0x80=128：拒绝呼叫，对方不在线
          // 0x81=129：拒绝呼叫，对方在通话中
          // 0x82=130：拒绝呼叫，中继信道忙
          // 0x83=131：拒绝呼叫，被优先级更高手台抢占时隙资源
          // 0x84=132: 拒绝呼叫，当前有更高优先级手台在通话中
          // 0x85=133: 拒绝呼叫,后台已经释放了此手台的通话资源
          // 0x86=134: 未登录,请先登录
          // 0x94=148: 拒绝呼叫，临时组或任务组已失效， 则终端应自行退出相应的组

          switch (msg) {
            case 0:
            case 2:
            case 1:
              this.setStartSpeakState()
              this.setTempListenGroup()

              // 定时检测本地讲话
              clearInterval(this.localRecordingTimer)
              const sl_is_local_recording = () => {
                this.voipServer &&
                  this.voipServer.sl_is_local_recording(localRecording => {
                    if (localRecording) {
                      // 本地讲话中，不处理
                      return
                    }

                    clearInterval(this.localRecordingTimer)

                    // 没有其他人在讲话，则结束讲话状态
                    if (this.speakState !== 2) {
                      this.setEndSpeakState()
                    }
                    this.checkCallSessionShouldExpired(1)
                  })
              }
              this.localRecordingTimer = setInterval(sl_is_local_recording, this.checkSpeakingInterval)

              break
            case 0x80:
              this.resumeOtherSpeakingStatus()
              bfNotify.messageBox(this.$t('msgbox.targetNotOnline'))
              break
            case 0x81:
              this.resumeOtherSpeakingStatus()
              bfNotify.messageBox(this.$t('msgbox.targetInCalling'))
              break
            case 0x82:
              this.resumeOtherSpeakingStatus()
              bfNotify.messageBox(this.$t('msgbox.repeaterBusy'))
              break
            case 0x83:
              this.setOtherSpeakState()
              bfNotify.messageBox(this.$t('msgbox.repeaterBusy'))
              break
            case 0x84:
              this.setOtherSpeakState()
              bfNotify.messageBox(this.$t('msgbox.repeaterBusy'))
              break
            case 0x85:
              this.resumeOtherSpeakingStatus()
              bfNotify.messageBox(this.$t('msgbox.repeaterBusy'))
              break
            case 0x86:
              this.resumeOtherSpeakingStatus()
              bfNotify.messageBox(this.$t('msgbox.targetNotLogin'))
              break
            case 0x94:
              this.resumeOtherSpeakingStatus()
              bfNotify.messageBox(this.$t('dynamicGroup.expiredDynamicGroup'))
              break
          }
        })
        // 监听其他人员讲话消息
        this.voipServer.sg_other_speaking.connect((speaker_dmr_id, speaker_target_dmr_id, start_end) => {
          bfglob.console.log('sg_other_speaking', speaker_dmr_id, speaker_target_dmr_id, start_end)

          if (start_end === 1) {
            // 其他人员讲话
            this.otherSpeaking = true
            this.setOtherSpeakState()

            // 记录当前的讲话人员id
            this.currentSpeaker = speaker_dmr_id
            this.setPendingTarget(speaker_dmr_id, speaker_target_dmr_id)

            // 定时检测其他人员是否还在讲话
            clearInterval(this.localPlayingTimer)
            const sl_is_local_playing = () => {
              this.voipServer &&
                this.voipServer.sl_is_local_playing(localPlaying => {
                  if (localPlaying) {
                    // 其他人员讲话，不处理
                    return
                  }

                  clearInterval(this.localPlayingTimer)

                  // 本地不处于讲话中，则结束讲话状态
                  if (this.speakState !== 1) {
                    this.setEndSpeakState()
                  }
                  this.checkCallSessionShouldExpired(1)
                })
            }
            this.localPlayingTimer = setInterval(sl_is_local_playing, this.checkSpeakingInterval)
          } else {
            // 其他人员结束讲话
            this.otherSpeaking = false
            if (this.currentSpeaker !== speaker_dmr_id) {
              return
            }
            this.setEndSpeakState()
            // 清除检测其他人员讲话定时器
            clearInterval(this.localPlayingTimer)

            this.checkCallSessionShouldExpired()
          }
        })
        // 接收在电话通话中取消收听组时，坐席无法正常接收指令时语音超时信号
        this.voipServer.sg_remote_speaking_timeout.connect(() => {
          this.checkCallSessionShouldExpired(1)
        })
        // 接收本地通话时间结束信息
        this.voipServer.sg_local_speaking_end.connect(() => {
          bfglob.console.log('sg_local_speaking_end')
          this.sg_local_speaking_end()
        })
        // 接收通话时间超时信息
        this.voipServer.sg_local_speaking_timeout.connect(() => {
          bfglob.console.log('sg_local_speaking_timeout')
          this.sg_local_speaking_end()
        })

        // 接收电话挂断信号
        this.voipServer.sg_endcall.connect((src_dmrid, target_dmrid, sound_type, phone_no) => {
          this.phoneEndCall(src_dmrid, target_dmrid, sound_type, phone_no)
        })

        // 接收缺失USB设备信息
        this.voipServer.sg_no_usb3000.connect(() => {
          // this.sl_getUsb3000Name();
          this.setUsbStatus('')
        })
        this.voipServer.sg_usb3000_found.connect(() => {
          this.sl_getUsb3000Name()
        })

        // 监听短信消息
        this.voipServer.sg_bc31.connect((send_time, sender, target, sms_no, content, is_group) => {
          // sender,target 可能出现小写字母，导致无法查找对应的目标
          sender = sender.toUpperCase()
          target = target.toUpperCase()

          // 设置消息通知参数
          const cls = bfCrypto.sha256(sender + target + sms_no + content + is_group)
          const getSenderName = () => {
            if (!sender) {
              return ''
            }
            if (sender === '00000000') {
              return this.$t('dialog.systemCenter')
            }

            const device = bfglob.gdevices.getDataByIndex(sender)
            return device && device.selfId ? device.selfId : ''
          }
          const confirmSms = () => {
            return new Promise(resolve => {
              resolve()
              if (is_group) {
                return
              }
              // 如果是单呼，则回应收到短信消息
              this.voipServer.sl_confirmSMS(sender, target, sms_no)
            })
          }
          let notify = Promise.resolve()
          // 创建消息vnode
          // const h = this.$createElement
          const message = h(
            'div',
            {
              class: {
                'notify-vnode-message': true,
              },
            },
            [
              h(
                'div',
                {
                  class: {
                    content: true,
                  },
                },
                `${content}`
              ),
              h(
                'div',
                {
                  class: {
                    footer: true,
                  },
                },
                [
                  h(
                    'div',
                    {
                      class: {
                        sender: true,
                      },
                    },
                    `${this.$t('dataTable.senderName')}: ${getSenderName()}`
                  ),
                  h(
                    ElButton,
                    {
                      onClick: () => {
                        confirmSms()
                          .then(() => notify)
                          .then(vm => vm && typeof vm.close === 'function' && vm.close())
                      },
                      props: {
                        size: 'mini',
                        round: true,
                      },
                      class: {
                        'confirm-button': true,
                      },
                    },
                    this.$t('dialog.confirm')
                  ),
                ]
              ),
            ]
          )

          // 创建消息通知
          notify = bfNotify.notifyBox({
            title: this.$t('dialog.smsNotification'),
            dangerouslyUseHTMLString: true,
            message,
            type: 'info',
            duration: 0,
            offset: 25,
            customClass: `sms-notification ${cls}`,
            position: 'top-left',
            iconClass: 'iconfont icon-sms',
          })
        })
      },

      // 通话逻辑
      setStartSpeakState() {
        this.speakState = 1
      },
      setEndSpeakState() {
        this.speakState = 0
      },
      setOtherSpeakState() {
        this.speakState = 2
      },
      toggleSpeaking() {
        if (this.speakState === 1) {
          this.sl_i_speak_end()
        } else {
          this.sl_i_speak_start()
        }
      },
      cancelPending() {
        // 取消挂起
        this.resumeTarget()
      },
      cleanPendingTarget() {
        this.pendingTarget = ''
      },
      resumeTarget() {
        this.cleanPendingTarget()
        this.cleanTempListenGroup()
      },
      resumeOtherSpeakingStatus() {
        if (this.otherSpeaking) {
          this.setOtherSpeakState()
        } else {
          this.setEndSpeakState()
        }
      },

      // 动画方法
      animateNextImage(i, len) {
        this.speakImagesIndex = i % len
        this.animateTimer = setTimeout(() => {
          this.animateNextImage(++i, len)
        }, 200)
      },
      startAnimate() {
        clearTimeout(this.animateTimer)
        this.animateNextImage(0, this.speakImages.length)
      },
      endAnimate() {
        clearTimeout(this.animateTimer)
        this.speakImagesIndex = 0
      },
      startSpeakStyle() {
        this.speakBtnType = 'danger'
        this.startAnimate()
      },
      endSpeakStyle() {
        this.speakBtnType = 'primary'
        this.endAnimate()
      },
      otherSpeakStyle() {
        this.speakBtnType = 'success'
        this.startAnimate()
      },
      updateTreeNode(device) {
        bfTree.updateDeviceNodeTitle(this.targetTreeId, device)
      },
      // 设置的组呼通话目标不在收听组列表中，需要临时添加，以便接收目标的回话
      cleanTempListenGroup() {
        this.tempListenGroup = ''
        this.sl_updateListenGroup()
      },
      setTempListenGroup() {
        // 跳过单呼目标
        const device = bfglob.gdevices.getDataByIndex(this.speakInfo.target)
        if (device) {
          return
        }
        // 组呼目标已存在收听组
        if (this.speakInfo.listenGroup.includes(this.speakInfo.target)) {
          return
        }

        // // 判断是否已经处理过临时收听组
        // if (this.tempListenGroup === this.speakInfo.target) {
        //   return
        // }

        this.tempListenGroup = this.speakInfo.target
        const tempListenGroup = [...this.speakInfo.listenGroup, this.speakInfo.target]
        // 添加临时的收听组目标
        this.sl_updateListenGroup(tempListenGroup)
      },
      // 比较收听组是否有变化
      compareListenGroupChanged(listenGroup, oldListenGroup) {
        if (listenGroup.length !== oldListenGroup.length) {
          return true
        }
        for (let i = 0; i < listenGroup.length; i++) {
          const item = listenGroup[i]
          const hasItem = oldListenGroup.includes(item)
          if (!hasItem) {
            return true
          }
        }
        return false
      },
      updateUserVoipSpeakInfo() {
        const skInfo = {}
        Object.assign(skInfo, this.speakInfo)
        skInfo.listenGroup = this.speakInfo.listenGroup.filter(item => !this.cmdAgentExLisGroup.includes(item))
        bfglob.userInfo.setting.voipSpeakInfo = skInfo
        bfglob.userInfo.setting.ispUdateVoipSpeakInfo = true
        bfProcess.updateUserSetting(JSON.stringify(bfglob.userInfo.setting), dbCmd.DB_USER_PUPDATE)
      },

      // 电话挂断和转接事件逻辑
      // 电话挂断
      telephoneHangup() {
        if (!this.voipServer) {
          return
        }
        this.voipServer.sl_end_call(this.phoneTransferBc15.phoneDmrId)
      },
      // 电话转接
      setTransferTarget(selectKeys) {
        let target = ''

        // 读取选中节点的目标dmrId
        const key = selectKeys.shift() || ''
        const orgItem = bfglob.gorgData.get(key)
        if (orgItem) {
          target = orgItem.dmrId || ''
        } else {
          const device = bfglob.gdevices.get(key)
          if (device) {
            target = device.dmrId || ''
          }
        }

        this.transferTargetTemp = target
      },
      slPhoneTransfer() {
        if (!this.voipServer || !this.transferTarget) {
          return
        }
        this.voipServer.sl_phone_transfer(this.phoneTransferBc15.phoneDmrId, this.phoneTransferBc15.targetDmrId, this.transferTarget)
      },
      // 设置来电显示号码
      setPhoneNo(phoneNo = '') {
        // 设置号码时，如果是系统内的数据，则添加对应的名称
        if (phoneNo) {
          const phoneBook = bfglob.gphoneBook.getDataByIndex(phoneNo)
          if (phoneBook && phoneBook.phoneName) {
            phoneNo = `${phoneBook.phoneName}/${phoneNo}`
          }
        }
        this.phoneNo = phoneNo
      },
      savePhoneTransferBc15(bc15_obj) {
        const hexsourceDmrid = bfutil.uint32DmrId2Hex(bc15_obj.sourceDmrid)
        const hexTargetDmrid = bfutil.uint32DmrId2Hex(bc15_obj.targetDmrid)
        this.phoneTransferBc15.phoneDmrId = hexsourceDmrid
        this.phoneTransferBc15.targetDmrId = hexTargetDmrid

        this.phoneTransferBc15.origin = bc15_obj
      },
      // 给本中心的来电开始/结束指令处理逻辑
      processGatewayTelephone(bc15_obj, _device) {
        this.savePhoneTransferBc15(bc15_obj)
        // 0：语音结束, 1：语音开始
        if (bc15_obj.callStatus === 1) {
          this.setPhoneNo(bc15_obj.phoneNo)
        }
      },
      phoneEndCall(src_dmrid, target_dmrid, sound_type, phone_no) {
        bfglob.console.log('phoneEndCall', src_dmrid, target_dmrid, sound_type, phone_no)

        this.checkCallSessionShouldExpired(1)
        this.setPhoneNo()

        // 如果当前处于选择转接目标界面，则关闭界面和清除转接目标参数
        if (this.selectNodeTarget === 3 && this.showTargetTree === true) {
          this.showTargetTree = false
          this.transferTarget = this.transferTargetTemp = ''
        }
      },

      addOneDeviceNode(device) {
        bfTree.addOneDeviceNode(this.targetTreeId, device, { selected: false })
        bfTree.renderOrgCounter(this.targetTreeId)
        bfTree.renderOrgCounter(this.sendGroupTreeId)
      },
      delOneDeviceNode(device) {
        bfTree.delOneDeviceNode(this.targetTreeId, device)
        bfTree.renderOrgCounter(this.targetTreeId)
        bfTree.renderOrgCounter(this.sendGroupTreeId)
      },
      updateOneDeviceNode(device) {
        bfTree.updateOneDeviceNode(this.targetTreeId, device)
        bfTree.renderOrgCounter(this.targetTreeId)
        bfTree.renderOrgCounter(this.sendGroupTreeId)
      },
      addOneOrgNode(orgData) {
        bfTree.addOneOrgNode(this.targetTreeId, orgData, { selected: false })
        bfTree.renderOrgCounter(this.targetTreeId)
        bfTree.addOneOrgNode(this.sendGroupTreeId, orgData, {
          ...listenGroupTreeNodeOption,
          selected: false,
        })
      },
      delOneOrgNode(key) {
        bfTree.delOneOrgNode(this.targetTreeId, key)
        bfTree.renderOrgCounter(this.targetTreeId)
        bfTree.delOneOrgNode(this.sendGroupTreeId, key)
        // 删除已经选中的接收组数据
        // 本地单位数据已经被删除，只能通过接收组数据遍历查找不存在对应单位数据的dmrId，然后再删除并更新
        const delDmrIdList = this.speakInfo.listenGroup.filter(dmrId => {
          return !bfglob.gorgData.getDataByIndex(dmrId)
        })
        while (delDmrIdList.length) {
          const dmrId = delDmrIdList.shift()
          const index = this.speakInfo.listenGroup.findIndex(k => k === dmrId)
          if (index > -1) {
            this.speakInfo.listenGroup.splice(index, 1)
          }
        }
        // 删除了目标终端的上级，则需要清除选中目标
        const targetData = bfglob.gdevices.getDataByIndex(this.speakInfo.target)
        if (targetData && targetData.orgId === key) {
          this.speakInfo.target = ''
        }
        // 删除了坐席终端的上级，则需要清除坐席终端数据
        const speakerData = bfglob.gdevices.getDataByIndex(this.speakInfo.speaker)
        if (speakerData && speakerData.orgId === key) {
          this.speakInfo.speaker = ''
        }
      },
      updateOneOrgNode(orgData) {
        bfTree.updateOneOrgNode(this.targetTreeId, orgData)
        bfTree.renderOrgCounter(this.targetTreeId)
        bfTree.updateOneOrgNode(this.sendGroupTreeId, orgData, listenGroupTreeNodeOption)
      },
      treeSortChildren() {
        bfTree.sortChildren(this.targetTreeId)
        bfTree.sortChildren(this.sendGroupTreeId)
      },
      // 动态组及成员操作
      addDynamicGroupNode(source) {
        addDynamicGroupNode(this.targetTreeId, source, { selected: false })
        addDynamicGroupNode(this.sendGroupTreeId, source, { selected: false })
      },
      updateDynamicGroupNode(source) {
        updateOneOrgNodeTitle(this.targetTreeId, source)
        updateOneOrgNodeTitle(this.sendGroupTreeId, source)
      },
      deleteDynamicGroupNode(source) {
        removeNode(this.targetTreeId, source.rid)
        removeNode(this.sendGroupTreeId, source.rid)

        // 同步通话目标和接收组
        if (source.dmrId === this.speakInfo.speaker) {
          this.speakInfo.speaker = ''
        }
        // 重置呼叫目标
        if (source.dmrId === this.speakInfo.target) {
          this.speakInfo.target = ''
          // // 判断上次呼叫的目标是否存在，存在则使用上次的目标
          // if (bfglob.gorgData.getDataByIndex(lastTarget) || bfglob.gorgData.getDataByIndex(lastTarget)) {
          //     this.speakInfo.target = lastTarget
          // }else{
          //   this.speakInfo.target = ''
          // }
        }
        this.speakInfo.listenGroup = this.speakInfo.listenGroup.filter(item => item !== source.dmrId)
      },
      gotDbDeviceUpdate(dbDevice) {
        // 判断该终端是否是自己的指挥座席
        if (dbDevice.dmrId !== this.speakInfo?.speaker) {
          return
        }
        this.speakInfo.speaker = ''
        this.$nextTick(() => {
          this.speakInfo.speaker = dbDevice.dmrId
          this.sl_updateListenGroup()
          this.updateUserVoipSpeakInfo()
        })
      },
    },
    watch: {
      voipServerConnected(val) {
        bfglob.emit('voipServerConnected', val)
      },
      visible(_val) {
        !this.setKeysEvent && this.initKeysEvent()
      },
      speakState(val) {
        switch (val) {
          case 1:
            this.startSpeakStyle()
            break
          case 2:
            this.otherSpeakStyle()
            break
          case 0:
          default:
            this.endSpeakStyle()
            break
        }
        bfglob.emit('sgSpeakingType', this.speakBtnType)
      },
      'speakInfo.speaker'(_val) {
        this.sl_setupUserInfo()
      },
      'speakInfo.priority'(_val) {
        this.sl_setupUserInfo()
      },
      'speakInfo.target'(_val) {
        this.sl_setupTargetDmrid()
        // this.setTempListenGroup()
        this.updateUserVoipSpeakInfo()
      },
      setupTarget(_val) {
        this.sl_setupTargetDmrid()
      },
      'speakInfo.maxSpeakTime'(_val) {
        this.sl_setMaxSpeakingTime()
        this.updateUserVoipSpeakInfo()
      },
      'speakInfo.listenGroup': {
        deep: true,
        handler(listenGroup, oldListenGroup) {
          if (this.compareListenGroupChanged(listenGroup, oldListenGroup)) {
            // this.setTempListenGroup()
            this.sl_updateListenGroup()
            this.updateUserVoipSpeakInfo()
          }
        },
      },
      voipServer(_val) {
        this.initVoipServer()
      },
    },
    computed: {
      cmdAgentSpeakDevice() {
        return bfglob.gdevices.getDataByIndex(this.speakInfo.speaker)
      },
      cmdAgentExLisGroup() {
        const skDevice = this.cmdAgentSpeakDevice
        let lsGroup = []
        if (skDevice && skDevice.channels) {
          lsGroup = cloneDeep(skDevice.channels.find(item => item.no === 1)?.listenGroup || [])
        }
        return lsGroup
      },
      listenGroupInfo() {
        const lsGroup = Array.from(new Set([...this.cmdAgentExLisGroup, ...this.speakInfo.listenGroup]))

        return lsGroup.map(dmrId => {
          return (bfglob.gorgData.getDataByIndex(dmrId) || bfglob.noPermOrgData.getDataByIndex(dmrId))?.orgShortName ?? ''
        })
      },
      dynamicGroup() {
        const dynamicGroup = bfglob.gorgData.getDataByIndex(this.speakInfo.target)
        return dynamicGroupTypes.includes(dynamicGroup?.orgIsVirtual) ? dynamicGroup : undefined
      },
      dynamicGroupMemberInfo() {
        const members = bfglob.gdynamicGroupDetail.getDataByGroupRid(this.dynamicGroup?.rid)
        return members.map(item => {
          // 根据数据类型，查找成员名称
          if (item.isDeviceGroup === 2) {
            return bfglob.gorgData.getDataMaybeNoPerm(item.groupRid)?.orgShortName ?? ''
          }
          return bfglob.gdevices.getDataMaybeNoPerm(item.deviceRid)?.selfId ?? ''
        })
      },
      disableDynamicGroupMember() {
        return !this.dynamicGroup
      },
      treeOption() {
        return {
          selectMode: 2,
        }
      },
      filterOption() {
        return {
          leavesOnly: false,
        }
      },
      targetTreeOption() {
        return {
          selectMode: 1,
        }
      },
      contextmenuOption() {
        return {
          menu: [
            {
              title: this.$t('tree.online'),
              cmd: 'displayOnline',
            },
            {
              title: this.$t('tree.displayAllDev'),
              cmd: 'displayAll',
            },
          ],
          select: (event, ui) => {
            switch (ui.cmd) {
              case 'displayOnline':
                this.$refs[this.targetTreeId]?.showOnlineDevices()
                break
              case 'displayAll':
                this.$refs[this.targetTreeId]?.showAllDevices()
            }
          },
        }
      },
      voipServer() {
        return qWebChannelObj && qWebChannelObj.server
      },
      setupTarget() {
        return this.pendingTarget || this.speakInfo.target
      },
      callbackTargetName() {
        const org = bfglob.gorgData.getDataByIndex(this.pendingTarget)
        if (org) {
          return org.orgShortName
        }
        const devcie = bfglob.gdevices.getDataByIndex(this.pendingTarget)
        if (devcie) {
          return devcie.selfId
        }

        return this.pendingTarget
      },
      speakDevice() {
        const device = bfglob.gdevices.getDataByIndex(this.speakInfo.speaker)
        return device && device.selfId ? device.selfId : ''
      },
      canSpeaking() {
        return this.voipServer && this.speakInfo.speaker && this.setupTarget && this.hasUsbDevice && this.voipServerConnected
      },
      disUsbTip() {
        return !this.hasUsbDevice || !this.voipServerConnected
      },
      groupTreeTitle() {
        let title = ''
        switch (this.selectNodeTarget) {
          case 1:
            // 通话目标
            title = this.$t('dialog.selectCallTarget')
            break
          case 2:
            // 接收组
            title = this.$t('dialog.selectListenGroup')
            break
          case 3:
            // 电话转接
            title = this.$t('dialog.initTransferTargetTree')
            break
        }
        return title
      },
      speakInfoFormLabelWidth() {
        return this.isFR ? '140px' : this.isEN ? '120px' : '120px'
      },
      listenGroupName() {
        const dmrId = this.speakInfo.listenGroup[0]
        const orgItem = bfglob.gorgData.getDataByIndex(dmrId)
        const len = this.speakInfo.listenGroup.length
        return orgItem ? `${orgItem.orgShortName} ${len > 1 ? '+' + (len - 1) : ''}` : ''
      },
      targetName() {
        const key = this.speakInfo.target

        // 判断是否为全呼目标
        if (key === bfglob.fullCallDmrId) {
          return this.$t('dialog.fullCall')
        }

        const orgItem = bfglob.gorgData.getDataByIndex(this.speakInfo.target)
        if (orgItem) {
          return orgItem.orgShortName || orgItem.dmrId || ''
        } else {
          const device = bfglob.gdevices.getDataByIndex(key)
          if (device) {
            return device.selfId || device.dmrId || ''
          }
        }

        return ''
      },
    },
    mounted() {
      bfglob.vspeaking = this
      this.updateUserVoipSpeakInfo = debounce(this.updateUserVoipSpeakInfo, 500)
      this.init()

      bfglob.on('bc15_speaker_pending', (bc15_obj, device) => {
        switch (bc15_obj.soundType) {
          case 0:
            // 0:默认的正常手台语音申请
            break
          case 1:
            // 1:手台电话会话语音
            break
          case 2:
            // 电话网关过来的开始/结束指令
            // 过滤不是打入本中心坐席的电话
            const hexDmrId = bfutil.uint32DmrId2Hex(bc15_obj.targetDmrid)
            if (hexDmrId !== this.speakInfo.speaker) {
              return
            }

            this.processGatewayTelephone(bc15_obj, device)
            break
        }
      })

      bfglob.on('bc15_call_timeout', device => {
        if (device.dmrId === this.speakInfo.speaker) {
          this.setEndSpeakState()
        }
      })
      bfglob.on('reset_voipSpeakInfo_speaker', (speaker, priority) => {
        this.speakInfo.speaker = speaker
        this.speakInfo.priority = priority
      })

      // 订阅各指令中更新终端节点状态事件
      bfglob.on('updateDeviceNodeTitle', this.updateTreeNode)
      // 同步树节点的增、删、改操作
      bfglob.on('addOneDeviceNode', this.addOneDeviceNode)
      bfglob.on('delOneDeviceNode', this.delOneDeviceNode)
      bfglob.on('updateOneDeviceNode', this.updateOneDeviceNode)
      bfglob.on('addOneOrgNode', this.addOneOrgNode)
      bfglob.on('delOneOrgNode', this.delOneOrgNode)
      bfglob.on('updateOneOrgNode', this.updateOneOrgNode)
      bfglob.on('treeSortChildren', this.treeSortChildren)

      //当终端更新/删除
      bfglob.on('update_global_deviceData', this.gotDbDeviceUpdate)

      //界面关闭按钮
      this.$nextTick(() => {
        const dialogEl = document.querySelector('.speaking-dialog .el-dialog__header')
        const closeBtn = dialogEl.querySelector('.el-dialog__close')
        closeBtn.addEventListener(
          'focus',
          () => {
            closeBtn.blur()
          },
          false
        )
      })
    },
    beforeUnmount() {
      bfglob.off('updateDeviceNodeTitle', this.updateTreeNode)
      bfglob.off('addOneDeviceNode', this.addOneDeviceNode)
      bfglob.off('delOneDeviceNode', this.delOneDeviceNode)
      bfglob.off('updateOneDeviceNode', this.updateOneDeviceNode)
      bfglob.off('addOneOrgNode', this.addOneOrgNode)
      bfglob.off('delOneOrgNode', this.delOneOrgNode)
      bfglob.off('updateOneOrgNode', this.updateOneOrgNode)

      bfglob.off('treeSortChildren', this.treeSortChildren)
      bfglob.off('update_global_deviceData', this.gotDbDeviceUpdate)
    },
  }
</script>

<style lang="scss">
  .el-dialog.speaking-dialog {
    width: 580px;
    .el-dialog__header {
      .el-dialog__title {
        font-size: 27px;
        font-family: 'AlibabaPuHuiTi2';
        background: linear-gradient(180deg, rgba(81, 224, 255, 0.52) 0%, rgba(81, 224, 255, 0.52) 22.66%, rgba(255, 255, 255, 0.52) 62.72%), #ffffff;
        -webkit-background-clip: text; /* 裁剪背景到文字 */
        -webkit-text-fill-color: transparent; /* 文字透明，让背景透出来 */
        text-shadow: 0px 0px 48px rgba(9, 171, 235, 0.61);
      }
    }
    .el-dialog__body {
      padding: 10px;
      // width:800px;
      display: flex;
      justify-content: center;
    }

    .el-form .el-form-item {
      margin-bottom: 8px;

      .el-input-number.max-speak-time {
        width: 100%;
      }

      .send-group-append.bf-input {
        border: none;
        border-radius: 0;
        box-shadow: 0 0 0 2px rgba(148, 204, 232, 1) inset;
        .el-input-group__prepend,
        .el-input-group__append {
          padding: 0;
          background-color: transparent;

          .target-input-btn {
            background-color: #1398e9;
            padding: 0;
            margin: 0;
            width: 30px;
            height: 30px;
            min-width: unset;

            &:last-child {
              margin-left: 12px;
            }
          }
        }
        .el-input-group__append {
          padding-right: 12px;
        }
        .el-input__wrapper {
          box-shadow: unset !important;
          input {
            text-align: center;
          }
        }
      }

      &.incoming-call {
        .el-form-item__content {
          display: flex;
          align-items: center;
        }

        .incoming-call-phoneNo {
          flex: auto;
        }

        .incoming-call-btns {
          flex: auto;
          min-width: 90px;
        }
      }

      &.speak-microphone {
        .el-form-item__content {
          display: block;
        }

        .animate-speak-box .animate-speak-btn {
          width: 76px;
          height: 100px;
          padding: 0;
          background-color: transparent;
          border: none;
        }

        .speak-prompt {
          line-height: 24px;
        }
      }

      .cancel-hang-icon,
      .usb-icon {
        width: 30px;
        height: 30px;
        background-color: #213f57;
        border-radius: 4px;
        span {
          line-height: 30px;
        }
      }
    }
  }

  .el-dialog.speak-target-dialog {
    height: 60vh;

    .el-dialog__body {
      height: calc(100% - 45px);
    }
  }

  .el-dialog.listen-group-dialog {
    height: 60vh;

    .el-dialog__body {
      height: calc(100% - 45px);
    }
  }

  .notify-vnode-message {
    .footer {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 6px;

      .sender {
        flex: auto;
      }

      .confirm-button {
        flex: auto;
        flex-grow: 0;
      }
    }
  }

  .listenGroupPopover {
    width: max-content !important;
    max-width: 320px;

    .el-popover__title {
      text-align: center;
      border-bottom: 1px solid #dcdfe6;
      padding-bottom: 6px;
    }

    .listenGroupList {
      max-height: 200px;
      overflow: auto;

      .listenGroupList-item {
        line-height: 20px;
      }
    }
  }
</style>
