<template>
  <bf-dialog
    v-model="visible"
    ref="bfSpeaking"
    :title="$t('dialog.networkSpeaking')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    modal-class="drag-dialog-modal"
    width="420px"
    append-to-body
    draggable
    center
  ></bf-dialog>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import bfDialog from '@/components/bfDialog/main'

  const visible = ref(false)
</script>

<style lang="scss"></style>
