{"nested": {"bfkcp": {"options": {"optimize_for": "LITE_RUNTIME"}, "nested": {"rpc_cmd": {"fields": {"seqNo": {"type": "int32", "id": 2}, "sid": {"type": "int64", "id": 3}, "cmd": {"type": "int32", "id": 5}, "res": {"type": "int32", "id": 8}, "body": {"type": "bytes", "id": 10}, "paraStr": {"type": "string", "id": 11}, "paraBin": {"type": "bytes", "id": 12}, "paraInt": {"type": "int64", "id": 13}}}, "login": {"fields": {"deviceDmrid": {"type": "fixed32", "id": 1}, "deviceName": {"type": "string", "id": 2}, "loginType": {"type": "int32", "id": 3}, "deviceModel": {"type": "string", "id": 4}, "password": {"type": "string", "id": 5}, "passwordMethod": {"type": "int32", "id": 6}, "timeStr": {"type": "string", "id": 7}, "sysId": {"type": "string", "id": 8}, "extraOption": {"rule": "repeated", "type": "int32", "id": 9}, "codec": {"rule": "repeated", "type": "int32", "id": 10}}}, "res_login_para_bin": {"fields": {"validSnCode": {"type": "bytes", "id": 1}, "imbeSn": {"type": "string", "id": 2}, "isHaveFullCallPerm": {"type": "int32", "id": 3}}}, "res_login": {"fields": {"resCode": {"type": "int32", "id": 1}, "sid": {"type": "int64", "id": 3}, "hangupTime": {"type": "int32", "id": 4}, "httpPort": {"type": "int32", "id": 5}, "serverVersion": {"type": "string", "id": 6}, "settingLastUpdateTime": {"type": "string", "id": 7}}}, "res_repeater_state": {"fields": {"deviceDmrid": {"type": "fixed32", "id": 1}, "channelId": {"type": "int32", "id": 2}, "rxFrequency": {"type": "fixed32", "id": 3}, "txFrequency": {"type": "fixed32", "id": 4}, "powerValue": {"type": "int32", "id": 5}, "ipAddr": {"type": "fixed32", "id": 6}, "volValue": {"type": "int32", "id": 7}, "tmpValue": {"type": "int32", "id": 8}, "tmpErr": {"type": "int32", "id": 9}, "antErr": {"type": "int32", "id": 10}, "gpsErr": {"type": "int32", "id": 11}, "volErr": {"type": "int32", "id": 12}, "rxPllErr": {"type": "int32", "id": 13}, "txPllErr": {"type": "int32", "id": 14}, "fanErr": {"type": "int32", "id": 15}, "signal": {"type": "int32", "id": 16}, "antValue": {"type": "int32", "id": 17}}}, "repeater_err_status": {"fields": {"deviceDmrid": {"type": "fixed32", "id": 1}, "tmpErr": {"type": "int32", "id": 2}, "antErr": {"type": "int32", "id": 3}, "gpsErr": {"type": "int32", "id": 4}, "volErr": {"type": "int32", "id": 5}, "rxPllErr": {"type": "int32", "id": 6}, "txPllErr": {"type": "int32", "id": 7}, "fanErr": {"type": "int32", "id": 8}, "signal": {"type": "int32", "id": 9}}}, "device_send": {"fields": {"repeaterDmrid": {"type": "fixed32", "id": 1}, "fsk": {"type": "bytes", "id": 3}, "sourceRepeaterDmrid": {"type": "fixed32", "id": 4}}}, "server_send": {"fields": {"fsk": {"type": "bytes", "id": 3}}}, "bc71": {"fields": {"repeaterDmrid": {"type": "fixed32", "id": 1}, "targetDmrid": {"type": "fixed32", "id": 2}, "sourceDmrid": {"type": "fixed32", "id": 3}, "fieldIntensity": {"type": "int32", "id": 4}, "supportDigital": {"type": "int32", "id": 5}, "supportAnalog": {"type": "int32", "id": 6}, "timeSlotNo": {"type": "int32", "id": 7}, "priority": {"type": "int32", "id": 8}, "soundType": {"type": "int32", "id": 9}, "phoneNo": {"type": "string", "id": 10}, "callDuplex": {"type": "int32", "id": 14}, "sourceRepeaterDmrid": {"type": "fixed32", "id": 15}, "preferInterruptTargetDmrid": {"type": "fixed32", "id": 16}}}, "cb71": {"fields": {"repeaterDmrid": {"type": "fixed32", "id": 1}, "targetDmrid": {"type": "fixed32", "id": 2}, "sourceDmrid": {"type": "fixed32", "id": 3}, "reqNo": {"type": "int32", "id": 4}, "supportDigital": {"type": "int32", "id": 5}, "supportAnalog": {"type": "int32", "id": 6}, "timeSlotNo": {"type": "int32", "id": 7}, "priority": {"type": "int32", "id": 8}, "result": {"type": "int32", "id": 9}, "interruptDmrid": {"type": "fixed32", "id": 10}, "soundType": {"type": "int32", "id": 11}, "phoneNo": {"type": "string", "id": 12}, "callDuplex": {"type": "int32", "id": 13}, "sourceRepeaterDmrid": {"type": "fixed32", "id": 14}}}, "bc73": {"fields": {"repeaterDmrid": {"type": "fixed32", "id": 1}, "targetDmrid": {"type": "fixed32", "id": 2}, "sourceDmrid": {"type": "fixed32", "id": 3}, "timeSlotNo": {"type": "int32", "id": 4}, "sourceRepeaterDmrid": {"type": "fixed32", "id": 5}}}, "cb75": {"fields": {"targetDmrid": {"type": "fixed32", "id": 2}, "sourceDmrid": {"type": "fixed32", "id": 3}, "reason": {"type": "int32", "id": 5}}}, "bc15": {"fields": {"repeaterDmrid": {"type": "fixed32", "id": 1}, "targetDmrid": {"type": "fixed32", "id": 2}, "sourceDmrid": {"type": "fixed32", "id": 3}, "fieldIntensity": {"type": "int32", "id": 4}, "supportDigital": {"type": "int32", "id": 5}, "supportAnalog": {"type": "int32", "id": 6}, "timeSlotNo": {"type": "int32", "id": 7}, "callType": {"type": "int32", "id": 9}, "priority": {"type": "int32", "id": 8}, "supportInterrupt": {"type": "int32", "id": 10}, "callStatus": {"type": "int32", "id": 11}, "soundType": {"type": "int32", "id": 12}, "phoneNo": {"type": "string", "id": 13}, "callDuplex": {"type": "int32", "id": 14}, "sourceRepeaterDmrid": {"type": "fixed32", "id": 15}, "startTime": {"type": "fixed64", "id": 16}}}, "bc10": {"fields": {"targetDmrid": {"type": "fixed32", "id": 2}, "sourceDmrid": {"type": "fixed32", "id": 3}, "callType": {"type": "int32", "id": 5}, "priority": {"type": "int32", "id": 6}, "supportInterrupt": {"type": "int32", "id": 7}, "frameNo": {"type": "fixed32", "id": 8}, "opusData_1": {"type": "bytes", "id": 9}, "opusData_2": {"type": "bytes", "id": 10}}}, "bc30": {"fields": {"repeaterDmrid": {"type": "fixed32", "id": 1}, "targetDmrid": {"type": "fixed32", "id": 2}, "sourceDmrid": {"type": "fixed32", "id": 3}, "timeSlotNo": {"type": "int32", "id": 4}, "callType": {"type": "int32", "id": 5}, "priority": {"type": "int32", "id": 6}, "supportInterrupt": {"type": "int32", "id": 7}, "frameNo": {"type": "fixed32", "id": 8}, "ambeData": {"type": "bytes", "id": 9}, "soundType": {"type": "int32", "id": 10}, "sourceRepeaterDmrid": {"type": "fixed32", "id": 11}}}, "dtmf": {"fields": {"repeaterDmrid": {"type": "fixed32", "id": 1}, "targetDmrid": {"type": "fixed32", "id": 2}, "sourceDmrid": {"type": "fixed32", "id": 3}, "dtmfStr": {"type": "string", "id": 6}, "sourceRepeaterDmrid": {"type": "fixed32", "id": 7}}}, "end_call": {"fields": {"repeaterDmrid": {"type": "fixed32", "id": 1}, "targetDmrid": {"type": "fixed32", "id": 2}, "sourceDmrid": {"type": "fixed32", "id": 3}, "soundType": {"type": "int32", "id": 12}, "phoneNo": {"type": "string", "id": 13}, "sourceRepeaterDmrid": {"type": "fixed32", "id": 14}}}, "phone_transfer": {"fields": {"phoneDmrid": {"type": "fixed32", "id": 2}, "nowTarget": {"type": "fixed32", "id": 3}, "transferTarget": {"type": "fixed32", "id": 4}}}, "OneChannelItem": {"fields": {"no": {"type": "int32", "id": 1}, "sendGroup": {"type": "string", "id": 2}, "listenGroup": {"rule": "repeated", "type": "string", "id": 3}, "zoneRid": {"type": "string", "id": 4}}}, "Channels": {"fields": {"channels": {"rule": "repeated", "type": "OneChannelItem", "id": 1}, "deviceDmrid": {"type": "string", "id": 2}, "devicePriority": {"type": "int32", "id": 3}}}, "PhoneAdapterShortNo2DmridReq": {"fields": {"shortNo": {"type": "string", "id": 1}, "opt": {"type": "int32", "id": 2}, "dmrid": {"type": "fixed32", "id": 3}}}, "PhoneAdapterShortNo2DmridRes": {"fields": {"result": {"type": "int32", "id": 1}, "dmrid": {"type": "fixed32", "id": 2}, "info": {"type": "string", "id": 3}}}, "PhoneLineSetting": {"fields": {"actionCode": {"type": "int32", "id": 1}, "linePos": {"rule": "repeated", "type": "int32", "id": 2}, "linePhoneNo": {"rule": "repeated", "type": "string", "id": 3}, "lineDmrid": {"rule": "repeated", "type": "fixed32", "id": 4}}}, "ex_one_org": {"fields": {"rid": {"type": "string", "id": 1}, "orgSelfId": {"type": "string", "id": 3}, "orgShortName": {"type": "string", "id": 5}, "orgFullName": {"type": "string", "id": 6}, "note": {"type": "string", "id": 7}, "orgIsVirtual": {"type": "int32", "id": 8}, "dmrId": {"type": "string", "id": 9}, "parentOrgId": {"type": "string", "id": 11}}}, "ex_one_device": {"fields": {"orgId": {"type": "string", "id": 3}, "selfId": {"type": "string", "id": 4}, "dmrId": {"type": "string", "id": 5}, "virOrgs": {"type": "string", "id": 6}, "note": {"type": "string", "id": 8}, "deviceType": {"type": "int32", "id": 9}, "priority": {"type": "int32", "id": 12}}}, "ambe_serial_code": {"fields": {"code": {"rule": "repeated", "type": "int32", "id": 1}}}, "ex_oneline_devices": {"fields": {"dmrId": {"rule": "repeated", "type": "int32", "id": 1}, "lastDataTime": {"rule": "repeated", "type": "int64", "id": 2}}}, "iot_data": {"fields": {"cmd": {"type": "sint32", "id": 1}, "devType": {"type": "sint32", "id": 2}, "devId": {"type": "bytes", "id": 3}, "cmdParam": {"type": "bytes", "id": 4}, "recvDevId": {"type": "bytes", "id": 9}, "recvTime": {"type": "sint64", "id": 10}, "kcpRecvDevId": {"type": "string", "id": 11}}}, "dev_data_info": {"fields": {"code": {"type": "int32", "id": 1}, "srcDmrid": {"type": "uint32", "id": 2}, "dstDmrid": {"type": "uint32", "id": 3}, "data": {"type": "bytes", "id": 4}}}, "addr_book": {"fields": {"type": {"type": "int32", "id": 1}, "code": {"type": "int32", "id": 2}, "body": {"type": "bytes", "id": 3}, "paraStr": {"type": "string", "id": 4}}}, "addr_book_list": {"fields": {"addrBookList": {"rule": "repeated", "type": "addr_book", "id": 1}}}, "bc40_req": {"fields": {"devDmrid": {"type": "fixed32", "id": 1}, "groupDmrid": {"type": "fixed32", "id": 2}, "riss": {"type": "int32", "id": 3}, "status": {"type": "bytes", "id": 4}, "powerEvent": {"type": "int32", "id": 5}, "lastPowerEvent": {"type": "int32", "id": 6}, "roaming": {"type": "int32", "id": 7}, "bsIndex": {"type": "int32", "id": 8}}}, "bc40_resp": {"fields": {"devDmrid": {"type": "fixed32", "id": 1}, "resCode": {"type": "int32", "id": 2}, "sysTime": {"type": "int64", "id": 3}}}, "Bf8100DmridInfo": {"fields": {"DmrID": {"type": "fixed32", "id": 1}, "Name": {"type": "string", "id": 2}, "OrgUUID": {"type": "string", "id": 4}, "MyUUID": {"type": "string", "id": 5}}}, "MESH_GPS_DATA_TYPE": {"values": {"ST_NONE": 0, "ST_FUNCTION_KEY": 1, "ST_POWER_ON": 2, "ST_POWER_OFF": 3, "ST_TIME": 4, "ST_DISTANCE": 5, "ST_FUNCTION_MENU": 6, "ST_PWDERR": 7, "ST_DEVICE_DISABLE": 8, "ST_REMOTE_MONITOR": 9, "ST_PTT_BEYOND": 10, "ST_LINK_BEYOND": 11, "ST_GPS_QUERY": 12, "ST_TX_ALARM": 13}}, "mesh_gps_info_t": {"fields": {"sourceId": {"type": "uint32", "id": 1}, "targetId": {"type": "uint32", "id": 2}, "hour": {"type": "uint32", "id": 3}, "minute": {"type": "uint32", "id": 4}, "second": {"type": "uint32", "id": 5}, "day": {"type": "uint32", "id": 6}, "month": {"type": "uint32", "id": 7}, "year": {"type": "uint32", "id": 8}, "available": {"type": "uint32", "id": 9}, "latitude": {"type": "uint32", "id": 10}, "northOrSouth": {"type": "uint32", "id": 11}, "longitude": {"type": "uint32", "id": 12}, "eastOrWest": {"type": "uint32", "id": 13}, "speed": {"type": "uint32", "id": 14}, "direction": {"type": "uint32", "id": 15}, "altitude": {"type": "int32", "id": 16}, "gpsDataType": {"type": "MESH_GPS_DATA_TYPE", "id": 17}}}, "MeshGpsInfo": {"fields": {"DmrID": {"type": "fixed32", "id": 1}, "GpsInfo": {"type": "mesh_gps_info_t", "id": 2}}}, "PocCommand": {"fields": {"cmd": {"type": "int32", "id": 1}, "seqNo": {"type": "int64", "id": 2}, "body": {"type": "bytes", "id": 3}, "paraStr": {"type": "string", "id": 4}, "paraBin": {"type": "bytes", "id": 5}, "paraInt": {"type": "int64", "id": 6}}}, "PocDefaultGroup": {"fields": {"defaultSendGroupDmrid": {"type": "fixed32", "id": 1}, "defaultListenGroupDmrids": {"rule": "repeated", "type": "fixed32", "id": 2}}}, "PocSubscribleUpdateOption": {"fields": {"frameNo": {"type": "int32", "id": 1}, "frameType": {"type": "int32", "id": 2}}}, "PocConfig": {"fields": {"canEditSubscriptionLocal": {"type": "int32", "id": 1}, "rgps": {"type": "int32", "id": 2}}}, "RepeatorOperationInfo": {"fields": {"operation": {"type": "int32", "id": 1}, "tableId": {"type": "int32", "id": 2}, "objIndex": {"type": "int32", "id": 3}, "objNum": {"type": "int32", "id": 4}}}, "ZoneId": {"fields": {"mainZoneId": {"type": "int32", "id": 1}, "subZoneId": {"type": "int32", "id": 2}, "userZoneId": {"type": "int32", "id": 3}}}, "RepeaterCurChInfo": {"fields": {"zoneId": {"type": "ZoneId", "id": 1}, "channelInfo": {"type": "RepeaterOneChannel", "id": 2}}}, "RepeaterCurChSet": {"fields": {"zoneId": {"type": "ZoneId", "id": 1}, "chId": {"type": "int32", "id": 2}}}, "RepeaterCurPowerSet": {"fields": {"txPower": {"type": "int32", "id": 1}, "curstomPowerLv": {"type": "int32", "id": 2}, "customVehiclePlv": {"type": "int32", "id": 3}}}, "RepeaterInfo": {"fields": {"version": {"type": "string", "id": 1}, "lowFrequency": {"type": "fixed32", "id": 2}, "highFrequency": {"type": "fixed32", "id": 3}, "sn": {"type": "bytes", "id": 4}, "deviceModel": {"type": "string", "id": 5}}}, "RepeaterCommonSetting": {"fields": {"devName": {"type": "string", "id": 1}, "dmrid": {"type": "fixed32", "id": 2}, "repeaterId": {"type": "fixed32", "id": 3}, "hangTime": {"type": "int32", "id": 4}, "soundTip": {"type": "int32", "id": 5}, "squelchLevel": {"type": "int32", "id": 6}, "defaultChannel": {"type": "int32", "id": 7}}}, "RepeaterKeyFunctionSetting": {"fields": {"longPressTime": {"type": "int32", "id": 1}, "key_01ShortPressFunc": {"type": "int32", "id": 2}, "key_01LongPressFunc": {"type": "int32", "id": 3}, "key_02ShortPressFunc": {"type": "int32", "id": 4}, "key_02LongPressFunc": {"type": "int32", "id": 5}, "key_03ShortPressFunc": {"type": "int32", "id": 6}, "key_03LongPressFunc": {"type": "int32", "id": 7}, "key_04ShortPressFunc": {"type": "int32", "id": 8}, "key_04LongPressFunc": {"type": "int32", "id": 9}}}, "RepeaterIpSetting": {"fields": {"ipMode": {"type": "int32", "id": 1}, "ipAddr": {"type": "fixed32", "id": 2}, "ipMask": {"type": "fixed32", "id": 3}, "ipGateway": {"type": "fixed32", "id": 4}, "ipDns": {"type": "fixed32", "id": 5}}}, "RepeaterServerSetting": {"fields": {"serverAddr": {"type": "string", "id": 1}, "serverPort": {"type": "int32", "id": 2}, "localPort": {"type": "int32", "id": 3}}}, "RepeaterOneChannel": {"fields": {"chId": {"type": "int32", "id": 1}, "chName": {"type": "string", "id": 2}, "chType": {"type": "int32", "id": 3}, "colourCodes": {"type": "int32", "id": 4}, "rxFrequency": {"type": "fixed32", "id": 5}, "txFrequency": {"type": "fixed32", "id": 6}, "txPower": {"type": "int32", "id": 7}}}, "SimulateDevice": {"fields": {"dmrid": {"type": "fixed32", "id": 1}, "currentCh": {"type": "int32", "id": 2}, "action": {"type": "int32", "id": 3}}}, "EPDB_CTRL_CLIENT_CMD": {"values": {"NONE": 0, "CLIENT_INFO": 1, "CH_CONFIG": 2, "CH_SWITCH": 3, "TX_POWER_SWITCH": 4, "DEVICE_REBOOT": 5, "CFCB_SYS_ADD": 6, "CFCB_SYS_REMOVE": 7, "SVT_RELAY_INFO": 8, "SVT_RELAY_CFG": 9, "SVT_RELAY_STATUS": 10}}, "RepeatorConfig": {"fields": {"deviceId": {"type": "uint32", "id": 1}, "ConfigDataBody": {"type": "bytes", "id": 2}, "dataMark": {"type": "int32", "id": 3}}}, "RepeatorConfigChInfo": {"fields": {"chType": {"type": "uint32", "id": 1}, "chName": {"type": "bytes", "id": 2}, "txPower": {"type": "uint32", "id": 3}, "customPower": {"type": "uint32", "id": 4}, "rxFreq": {"type": "fixed32", "id": 5}, "txFreq": {"type": "fixed32", "id": 6}, "cc": {"type": "uint32", "id": 7}}}, "RepeatorConfigChSwitch": {"fields": {"zoneId": {"type": "uint32", "id": 1}, "chId": {"type": "uint32", "id": 2}}}, "RepeatorConfigTxPowerSwitch": {"fields": {"txPower": {"type": "uint32", "id": 1}, "customPower": {"type": "uint32", "id": 2}}}, "ClientRepeatorInfoReport": {"fields": {"deviceId": {"type": "uint32", "id": 1}, "ipAddr": {"type": "uint32", "id": 2}, "rxFreq": {"type": "uint32", "id": 3}, "txFreq": {"type": "uint32", "id": 4}, "isReg": {"type": "int32", "id": 5}, "cfcbDisable": {"type": "int32", "id": 6}, "deviceModel": {"type": "string", "id": 7}, "dualslotIpmc": {"type": "int32", "id": 8}, "slotId": {"type": "int32", "id": 9}}}, "rpc_cmd_svt": {"fields": {"seqNo": {"type": "int32", "id": 2}, "sid": {"type": "int64", "id": 3}, "cmd": {"type": "int32", "id": 5}, "res": {"type": "int32", "id": 8}, "body": {"type": "bytes", "id": 10}, "paraStr": {"type": "string", "id": 11}, "paraBin": {"type": "bytes", "id": 12}, "paraInt": {"type": "int64", "id": 13}, "contollerNetwork": {"type": "int32", "id": 14}, "freerelayChnum": {"type": "int32", "id": 15}, "regStatus": {"type": "int32", "id": 16}, "busyStatus": {"type": "int32", "id": 17}, "moduleStatus": {"type": "int32", "id": 18}}}, "cb71_svt": {"fields": {"repeaterDmrid": {"type": "fixed32", "id": 1}, "targetDmrid": {"type": "fixed32", "id": 2}, "sourceDmrid": {"type": "fixed32", "id": 3}, "reqNo": {"type": "int32", "id": 4}, "supportDigital": {"type": "int32", "id": 5}, "supportAnalog": {"type": "int32", "id": 6}, "timeSlotNo": {"type": "int32", "id": 7}, "priority": {"type": "int32", "id": 8}, "result": {"type": "int32", "id": 9}, "interruptDmrid": {"type": "fixed32", "id": 10}, "soundType": {"type": "int32", "id": 11}, "phoneNo": {"type": "string", "id": 12}, "callDuplex": {"type": "int32", "id": 13}, "relayChnum": {"type": "int32", "id": 14}, "callType": {"type": "int32", "id": 15}, "moveFreerelay": {"type": "int32", "id": 16}}}, "svt_login": {"fields": {"deviceDmrid": {"type": "fixed32", "id": 1}, "deviceCode": {"type": "bytes", "id": 2}, "ulRxFre": {"type": "fixed32", "id": 3}, "ulTxFre": {"type": "fixed32", "id": 4}}}, "res_svt_login": {"fields": {"resCode": {"type": "int32", "id": 1}, "ucSeqId": {"type": "int32", "id": 2}, "ucChNum": {"type": "int32", "id": 3}, "deviceId": {"type": "fixed32", "id": 4}, "usHbPeriod": {"type": "int32", "id": 5}, "ulTime": {"type": "int32", "id": 6}, "ubUnityCfg": {"type": "int32", "id": 7}, "usChHangTime": {"type": "int32", "id": 8}, "usPrivateHangTime": {"type": "int32", "id": 9}, "usGroupHangTime": {"type": "int32", "id": 10}, "usEmerHangTime": {"type": "int32", "id": 11}, "usAutoSignalDurtion": {"type": "int32", "id": 12}, "usAutoSignalInterval": {"type": "int32", "id": 13}, "ubAuthEnable": {"type": "int32", "id": 14}, "ucnAuthKey": {"type": "bytes", "id": 15}}}, "svt_session": {"fields": {"ucNum": {"type": "int32", "id": 1}, "ucSizeof": {"type": "int32", "id": 2}, "ucnData": {"type": "bytes", "id": 3}}}, "SvtRepeatorInfoReport": {"fields": {"deviceId": {"type": "fixed32", "id": 1}, "ipAddr": {"type": "fixed32", "id": 2}, "rxFreq": {"type": "fixed32", "id": 3}, "txFreq": {"type": "fixed32", "id": 4}, "isReg": {"type": "int32", "id": 5}, "deviceCode": {"type": "bytes", "id": 6}, "ctrlId": {"type": "fixed32", "id": 7}, "moduleStatus": {"type": "fixed32", "id": 8}}}, "SvtExtendedSubscribles": {"fields": {"code": {"type": "int32", "id": 1}, "subscribles": {"rule": "repeated", "type": "fixed32", "id": 2}}}, "SvtRepeatorCfgPara": {"fields": {"chHangTime": {"type": "int32", "id": 1}, "privateHangTime": {"type": "int32", "id": 2}, "groupHangTime": {"type": "int32", "id": 3}, "emerHangTime": {"type": "int32", "id": 4}, "autoSignalDurtion": {"type": "int32", "id": 5}, "autoSignalInterval": {"type": "int32", "id": 6}, "rxFreq": {"type": "fixed32", "id": 7}, "txFreq": {"type": "fixed32", "id": 8}, "cc": {"type": "int32", "id": 9}, "ubAuthEnable": {"type": "int32", "id": 10}, "ucnAuthKey": {"type": "bytes", "id": 11}}}, "SvtRepeatorDevStatReport": {"fields": {"deviceId": {"type": "fixed32", "id": 1}, "antStatus": {"type": "int32", "id": 2}, "tempStatus": {"type": "int32", "id": 3}, "gpsStatus": {"type": "int32", "id": 4}, "fanStatus": {"type": "int32", "id": 5}, "txStatus": {"type": "int32", "id": 6}, "rxStatus": {"type": "int32", "id": 7}, "volStatus": {"type": "int32", "id": 8}, "pllStatus": {"type": "int32", "id": 9}, "sessionSlot1": {"type": "int32", "id": 10}, "sessionSlot2": {"type": "int32", "id": 11}, "freeRelay": {"type": "int32", "id": 12}}}}}, "bfdx_proto": {"options": {"optimize_for": "LITE_RUNTIME", "go_package": "bf8000/bfdx_proto"}, "nested": {"gps84": {"fields": {"gpsTime": {"type": "string", "id": 1}, "av": {"type": "int32", "id": 2}, "lat": {"type": "double", "id": 3}, "lon": {"type": "double", "id": 4}, "speed": {"type": "double", "id": 5}, "direction": {"type": "int32", "id": 6}, "altitude": {"type": "int32", "id": 7}}}, "bcxx_head": {"fields": {"xx": {"type": "string", "id": 1}, "bDic": {"type": "int32", "id": 2}, "cmd": {"type": "int32", "id": 3}, "sysId": {"type": "string", "id": 4}, "giFlag": {"type": "int32", "id": 5}, "cConCh": {"type": "string", "id": 6}, "conCh": {"type": "string", "id": 7}, "mIdT": {"type": "string", "id": 8}, "mIdS": {"type": "string", "id": 9}, "cmdTime": {"type": "string", "id": 10}, "mTxE": {"type": "int32", "id": 11}, "msStatus": {"type": "string", "id": 12}, "origData": {"type": "bytes", "id": 13}}}, "cmd_repeate_received": {"fields": {"dmrId": {"type": "string", "id": 1}, "cmd": {"type": "string", "id": 2}, "cDic": {"type": "string", "id": 3}, "sysId": {"type": "string", "id": 4}, "cmdBytes": {"type": "bytes", "id": 5}}}, "not_register_device_cmd": {"fields": {"dmrId": {"type": "string", "id": 1}, "cmdBytes": {"type": "bytes", "id": 2}, "receivedControllerDmrId": {"type": "string", "id": 3}}}, "new_controller": {"fields": {"dmrId": {"type": "string", "id": 1}, "ipInfo": {"type": "string", "id": 2}, "model": {"type": "string", "id": 3}, "deviceName": {"type": "string", "id": 4}}}, "bc00": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "cDic": {"type": "int32", "id": 3}, "cbxx": {"type": "int32", "id": 4}}}, "bc01": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}}}, "bc02": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "yN": {"type": "int32", "id": 3}, "time": {"type": "int32", "id": 4}, "size": {"type": "int32", "id": 5}}}, "bc03": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}}}, "bc04": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "tp": {"type": "int32", "id": 3}, "yN": {"type": "int32", "id": 4}, "penN": {"type": "int32", "id": 5}, "time": {"type": "int32", "id": 6}, "minLat": {"type": "double", "id": 7}, "minLon": {"type": "double", "id": 8}, "maxLat": {"type": "double", "id": 9}, "maxLon": {"type": "double", "id": 10}}}, "bc05": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "tp": {"type": "int32", "id": 3}, "yN": {"type": "int32", "id": 4}, "time": {"type": "int32", "id": 5}, "lat": {"type": "double", "id": 6}, "lon": {"type": "double", "id": 7}, "latDif": {"type": "string", "id": 8}, "lonDif": {"type": "string", "id": 9}}}, "bc06": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "tp": {"type": "int32", "id": 3}, "yN": {"type": "int32", "id": 4}, "time": {"type": "int32", "id": 5}}}, "bc07": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "yN": {"type": "int32", "id": 3}, "jtTime": {"type": "int32", "id": 4}, "dwTime": {"type": "int32", "id": 5}}}, "bc08": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "yN": {"type": "int32", "id": 4}, "jtCh": {"type": "int32", "id": 5}, "time": {"type": "int32", "id": 6}}}, "bc09": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "yN": {"type": "int32", "id": 3}, "st": {"type": "int32", "id": 4}}}, "bc10": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "cDic": {"type": "int32", "id": 3}, "cbxx": {"type": "int32", "id": 4}}}, "bc11": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "yN": {"type": "int32", "id": 2}, "cTp": {"type": "bytes", "id": 3}, "aTp": {"type": "int32", "id": 4}, "ddCh": {"type": "int32", "id": 5}}}, "bc12": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "pS": {"type": "int32", "id": 3}, "lpS": {"type": "int32", "id": 4}, "lsTime": {"type": "string", "id": 5}, "sellId": {"type": "string", "id": 6}, "licence": {"type": "string", "id": 7}, "devGroup": {"type": "fixed32", "id": 8}, "roaming": {"type": "int32", "id": 9}, "fromBc40": {"type": "bool", "id": 10}}}, "bc13": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "type": {"type": "int32", "id": 3}, "landBs": {"type": "int32", "id": 4}, "landCh": {"type": "int32", "id": 5}, "devGroup": {"type": "fixed32", "id": 6}, "roaming": {"type": "int32", "id": 7}, "fromBc40": {"type": "bool", "id": 8}}}, "bc14": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "landBs": {"type": "int32", "id": 3}, "audioCm": {"type": "int32", "id": 4}}}, "bc15": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "voiceDType": {"type": "int32", "id": 2}, "voiceAType": {"type": "int32", "id": 3}, "callType": {"type": "int32", "id": 4}, "callStatus": {"type": "int32", "id": 5}}}, "bc16": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}}}, "bc17": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "yN": {"type": "int32", "id": 2}, "userSt": {"type": "string", "id": 3}, "userTp": {"type": "int32", "id": 4}, "userAddrId": {"type": "string", "id": 5}, "license": {"type": "string", "id": 6}, "userShowId": {"type": "string", "id": 7}}}, "bc18": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "alarm": {"type": "int32", "id": 3}, "dbRid": {"type": "string", "id": 4}}}, "bc19": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "yN": {"type": "int32", "id": 3}}}, "bc20": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "yN": {"type": "int32", "id": 3}, "linkTp": {"type": "int32", "id": 4}, "aTp": {"type": "int32", "id": 5}, "netId": {"type": "string", "id": 6}}}, "bc21": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "cDic": {"type": "int32", "id": 3}, "cbxx": {"type": "int32", "id": 4}}}, "bc22": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "cDic": {"type": "int32", "id": 3}, "cbxx": {"type": "int32", "id": 4}, "yN": {"type": "int32", "id": 5}, "checkSt": {"type": "int32", "id": 6}}}, "bc23": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "conStatus": {"type": "string", "id": 2}, "cDic": {"type": "int32", "id": 3}, "cbxx": {"type": "int32", "id": 4}}}, "bc25": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "yN": {"type": "int32", "id": 2}, "pointN": {"type": "int32", "id": 3}, "pointCard": {"type": "string", "id": 4}, "lat": {"type": "double", "id": 5}, "lon": {"type": "double", "id": 6}, "latDif": {"type": "string", "id": 7}, "lonDif": {"type": "string", "id": 8}}}, "bc26": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "yN": {"type": "int32", "id": 2}, "ch": {"type": "int32", "id": 3}, "codeTp": {"type": "int32", "id": 4}, "chName": {"type": "bytes", "id": 5}}}, "bc28": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "yN": {"type": "int32", "id": 2}, "codeTp": {"type": "int32", "id": 3}, "cByte": {"type": "int32", "id": 4}, "data": {"type": "bytes", "id": 5}}}, "bc29": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "yN": {"type": "int32", "id": 2}, "keyId": {"type": "string", "id": 3}}}, "bc31": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "smsType": {"type": "string", "id": 2}, "smsContent": {"type": "string", "id": 3}, "smsNo": {"type": "string", "id": 4}}}, "bc38": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "mGroupId": {"type": "string", "id": 3}}}, "bc39": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "mGroupId": {"type": "string", "id": 3}}}, "bc42": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "code": {"type": "int32", "id": 2}}}, "dc00": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "gps": {"type": "gps84", "id": 2}, "cDic": {"type": "int32", "id": 3}, "cdxx": {"type": "int32", "id": 4}}}, "dc01_one_rfid": {"fields": {"readTime": {"type": "string", "id": 1}, "rfidId": {"type": "string", "id": 2}, "rfidType": {"type": "int32", "id": 3}, "dbType": {"type": "int32", "id": 4}}}, "dc01": {"fields": {"head": {"type": "bcxx_head", "id": 1}, "backlogPoints": {"type": "int32", "id": 2}, "rfids": {"rule": "repeated", "type": "dc01_one_rfid", "id": 4}}}, "cbxx_target": {"fields": {"targetGroud": {"rule": "repeated", "type": "string", "id": 1}, "targetDevice": {"rule": "repeated", "type": "string", "id": 2}}}, "cb01": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "time": {"type": "int32", "id": 2}, "size": {"type": "int32", "id": 3}, "count": {"type": "int32", "id": 4}}}, "cb02": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 3}, "time": {"type": "int32", "id": 4}, "size": {"type": "int32", "id": 5}}}, "cb03": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "minLat": {"type": "double", "id": 7}, "minLon": {"type": "double", "id": 8}, "maxLat": {"type": "double", "id": 9}, "maxLon": {"type": "double", "id": 10}}}, "cb04": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 4}, "penN": {"type": "int32", "id": 5}, "time": {"type": "int32", "id": 6}, "minLat": {"type": "double", "id": 7}, "minLon": {"type": "double", "id": 8}, "maxLat": {"type": "double", "id": 9}, "maxLon": {"type": "double", "id": 10}}}, "cb05": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 4}, "time": {"type": "int32", "id": 5}, "lat": {"type": "double", "id": 6}, "lon": {"type": "double", "id": 7}, "latDif": {"type": "string", "id": 8}, "lonDif": {"type": "string", "id": 9}}}, "cb06": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 4}, "time": {"type": "int32", "id": 5}, "latDif": {"type": "string", "id": 8}, "lonDif": {"type": "string", "id": 9}}}, "cb07": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 3}, "jtTime": {"type": "int32", "id": 4}, "dwTime": {"type": "int32", "id": 5}}}, "cb08": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "src": {"type": "string", "id": 2}, "yN": {"type": "int32", "id": 4}, "jtCh": {"type": "int32", "id": 5}, "time": {"type": "int32", "id": 6}}}, "cb09": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 3}, "st": {"type": "int32", "id": 4}}}, "cb10": {"fields": {"target": {"type": "cbxx_target", "id": 1}}}, "cb11": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 2}, "cTp": {"type": "bytes", "id": 3}, "aTp": {"type": "int32", "id": 4}, "ddCh": {"type": "int32", "id": 5}, "initiator": {"type": "string", "id": 6}, "origCmd": {"type": "bytes", "id": 7}}}, "cb12": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "type": {"type": "int32", "id": 2}, "bsId": {"type": "int32", "id": 3}, "bsN": {"type": "int32", "id": 4}, "chId": {"type": "int32", "id": 5}, "chN": {"type": "int32", "id": 6}, "conStatus": {"type": "string", "id": 7}, "myxb_TT": {"type": "int32", "id": 8}, "cBsN": {"type": "string", "id": 9}, "chDdSt": {"type": "int32", "id": 10}}}, "cb17": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 2}, "userSt": {"type": "string", "id": 3}, "userTp": {"type": "int32", "id": 4}, "userAddrId": {"type": "string", "id": 5}, "license": {"type": "string", "id": 6}, "userShowId": {"type": "string", "id": 7}}}, "cb19": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "type": {"type": "int32", "id": 2}, "newDuty": {"type": "string", "id": 3}, "oldDuty": {"type": "string", "id": 4}}}, "cb20": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 3}, "linkTp": {"type": "int32", "id": 4}, "aTp": {"type": "int32", "id": 5}, "netId": {"type": "string", "id": 6}}}, "cb21": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 3}, "ddCh": {"type": "int32", "id": 4}}}, "cb24": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 2}, "data": {"type": "string", "id": 3}, "codeTp": {"type": "int32", "id": 4}, "scheduleSendTime": {"type": "string", "id": 5}}}, "cb25": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 2}, "pointN": {"type": "int32", "id": 3}, "pointCard": {"type": "string", "id": 4}, "lat": {"type": "double", "id": 5}, "lon": {"type": "double", "id": 6}, "latDif": {"type": "string", "id": 7}, "lonDif": {"type": "string", "id": 8}}}, "cb26": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "yN": {"type": "int32", "id": 2}, "ch": {"type": "int32", "id": 3}, "chName": {"type": "string", "id": 5}}}, "cb42": {"fields": {"target": {"type": "cbxx_target", "id": 1}, "code": {"type": "int32", "id": 2}}}, "cbxx_send_stub": {"fields": {"cbxx": {"type": "string", "id": 1}, "targetGroup": {"rule": "repeated", "type": "string", "id": 2}, "targetGroupSeqNo": {"rule": "repeated", "type": "string", "id": 3}, "targetDevice": {"rule": "repeated", "type": "string", "id": 4}, "targetDeviceSeqNo": {"rule": "repeated", "type": "string", "id": 5}}}, "cc01": {"fields": {"actionCode": {"type": "int32", "id": 1}, "dmrId": {"type": "string", "id": 2}, "ccxxStr": {"type": "string", "id": 3}}}, "cc81": {"fields": {"targetDmrId": {"type": "uint32", "id": 1}, "applyDmrId": {"type": "uint32", "id": 2}}}, "res_gps_permission": {"fields": {"code": {"type": "int32", "id": 1}, "dmrid": {"type": "uint32", "id": 2}, "grantUserRid": {"type": "string", "id": 3}, "grantUserName": {"type": "string", "id": 4}, "expireTime": {"type": "string", "id": 5}}}, "cc83": {"fields": {"actionCode": {"type": "int32", "id": 1}, "hexDmrids": {"rule": "repeated", "type": "string", "id": 2}, "models": {"rule": "repeated", "type": "string", "id": 3}, "functions": {"rule": "repeated", "type": "string", "id": 4}}}, "unknown_iot_device_cmd": {"fields": {"devType": {"type": "int32", "id": 1}, "devId": {"type": "string", "id": 2}}}, "over_step_base_station": {"fields": {"devType": {"type": "int32", "id": 1}, "devId": {"type": "string", "id": 2}, "overStepType": {"type": "int32", "id": 4}}}, "SipGroupSubscribeOperation": {"fields": {"operation": {"type": "int32", "id": 1}, "dmrids": {"rule": "repeated", "type": "string", "id": 2}, "res": {"type": "int32", "id": 3}, "resMsg": {"type": "string", "id": 4}}}, "client_login_request": {"fields": {"sysId": {"type": "string", "id": 1}, "userName": {"type": "string", "id": 2}, "timeStr": {"type": "string", "id": 3}, "passHash": {"type": "string", "id": 4}, "loginMethod": {"type": "int32", "id": 5}, "sid": {"type": "string", "id": 6}, "loginWay": {"type": "int32", "id": 7}}}, "client_login_response": {"fields": {"responseCode": {"type": "int32", "id": 1}, "sid": {"type": "string", "id": 2}, "orgId": {"type": "string", "id": 3}, "userRid": {"type": "string", "id": 4}, "lic": {"type": "lic_response", "id": 5}, "serverVersion": {"type": "string", "id": 6}, "dbUser": {"type": "db_user", "id": 7}}}, "client_privilege_request": {"fields": {"sid": {"type": "string", "id": 1}, "userRid": {"type": "string", "id": 2}}}, "client_privilege_response": {"fields": {"responseCode": {"type": "int32", "id": 1}, "userOrgs": {"rule": "repeated", "type": "db_org", "id": 2}}}, "search_filter": {"fields": {"fromTime": {"type": "string", "id": 1}, "toTime": {"type": "string", "id": 2}, "deviceRid": {"type": "string", "id": 3}, "personRid": {"type": "string", "id": 4}, "timezone": {"type": "int32", "id": 5}, "orgId": {"type": "string", "id": 6}, "optPointId": {"type": "string", "id": 7}, "optGroupDmrId": {"type": "string", "id": 8}, "optRuleRid": {"type": "string", "id": 9}, "optLineRid": {"type": "string", "id": 10}, "optSmsDbType": {"type": "int32", "id": 11}}}, "rule_check_item": {"fields": {"no": {"type": "int32", "id": 1}, "pointRid": {"type": "string", "id": 2}, "checkTime": {"type": "string", "id": 3}, "deviceRid": {"type": "string", "id": 4}, "checkPersonRid": {"type": "string", "id": 5}, "checkResult": {"type": "int32", "id": 6}, "checkTimeEarly": {"type": "string", "id": 7}, "checkTimeEnd": {"type": "string", "id": 8}, "processCode": {"type": "int32", "id": 14}}}, "rule_statistic_result": {"fields": {"no": {"type": "int32", "id": 1}, "ruleRid": {"type": "string", "id": 2}, "lineRid": {"type": "string", "id": 3}, "pointTotal": {"type": "int32", "id": 4}, "pointOk": {"type": "int32", "id": 5}, "pointBad": {"type": "int32", "id": 6}, "timeStart": {"type": "string", "id": 7}, "timeEnd": {"type": "string", "id": 8}, "checkItems": {"rule": "repeated", "type": "rule_check_item", "id": 9}}}, "conference_dispatch": {"fields": {"userOrgId": {"type": "string", "id": 11}, "userUuid": {"type": "string", "id": 1}, "action": {"type": "int32", "id": 2}, "roomNo": {"type": "string", "id": 3}, "roomType": {"type": "int32", "id": 4}, "controllers": {"rule": "repeated", "type": "string", "id": 5}}}, "conference_dispatch_response": {"fields": {"confNo": {"type": "string", "id": 1}, "dispatchUuid": {"type": "string", "id": 2}, "response": {"type": "int32", "id": 3}}}, "controller_server_info": {"fields": {"logicHostAddr": {"type": "string", "id": 1}, "logicHostPort": {"type": "int32", "id": 2}, "voipHostAddr": {"type": "string", "id": 3}, "voipHostPort": {"type": "int32", "id": 4}, "code": {"type": "int32", "id": 5}}}, "rid_filter": {"fields": {"rids": {"rule": "repeated", "type": "string", "id": 1}, "requestCode": {"type": "int32", "id": 2}, "resultCols": {"rule": "repeated", "type": "string", "id": 3}}}, "member_can_add": {"fields": {"userPriority": {"type": "int32", "id": 1}, "member": {"type": "db_dynamic_group_detail", "id": 2}}}, "modify_device_list": {"fields": {"dynamicGroup": {"type": "db_org", "id": 1}, "userPriority": {"type": "int32", "id": 2}, "devices": {"rule": "repeated", "type": "db_dynamic_group_detail", "id": 3}, "groups": {"rule": "repeated", "type": "db_dynamic_group_detail", "id": 4}}}, "modify_member_list": {"fields": {"dynamicGroup": {"type": "db_org", "id": 1}, "addDevices": {"type": "db_dynamic_group_detail_list", "id": 3}, "addGroups": {"type": "db_dynamic_group_detail_list", "id": 4}, "delDevices": {"type": "db_dynamic_group_detail_list", "id": 5}, "delGroups": {"type": "db_dynamic_group_detail_list", "id": 6}}}, "temp_group_list": {"fields": {"dynamicGroup": {"rule": "repeated", "type": "db_org", "id": 1}, "members": {"rule": "repeated", "type": "db_dynamic_group_detail", "id": 2}}}, "DynamicGroupInfo": {"fields": {"groupDmrid": {"type": "string", "id": 1}, "groupName": {"type": "string", "id": 2}, "groupType": {"type": "fixed32", "id": 3}, "groupDmrids": {"rule": "repeated", "type": "string", "id": 4}, "deviceDmrids": {"rule": "repeated", "type": "string", "id": 5}}}, "phone_short_no_info": {"fields": {"shortNo": {"type": "string", "id": 1}, "dmrid": {"type": "string", "id": 2}}}, "poc_setting": {"fields": {"txGroupDmrid": {"type": "string", "id": 1}, "rxGroupDmrids": {"rule": "repeated", "type": "string", "id": 2}, "password": {"type": "string", "id": 3}, "pocIndividualContacts": {"rule": "repeated", "type": "string", "id": 4}, "pocGroupContacts": {"rule": "repeated", "type": "string", "id": 5}}}, "db_sys_config": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "confKey": {"type": "string", "id": 3}, "confValue": {"type": "string", "id": 4}}}, "db_table_operate_time": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "tableName": {"type": "string", "id": 3}, "lastModifyTime": {"type": "string", "id": 4}, "lastModifyOperation": {"type": "int32", "id": 5}, "lastModifyRows": {"type": "int32", "id": 6}}}, "db_org": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "orgSelfId": {"type": "string", "id": 3}, "orgSortValue": {"type": "int32", "id": 4}, "orgShortName": {"type": "string", "id": 5}, "orgFullName": {"type": "string", "id": 6}, "note": {"type": "string", "id": 7}, "orgIsVirtual": {"type": "int32", "id": 8}, "dmrId": {"type": "string", "id": 9}, "orgImg": {"type": "string", "id": 10}, "parentOrgId": {"type": "string", "id": 11}, "setting": {"type": "string", "id": 12}, "dynamicGroupState": {"type": "int32", "id": 13}, "creator": {"type": "string", "id": 14}}}, "db_image": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 3}, "fileName": {"type": "string", "id": 4}, "fileContent": {"type": "string", "id": 5}, "hash": {"type": "string", "id": 6}}}, "db_base_station": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "updateAt": {"type": "string", "id": 3}, "selfId": {"type": "string", "id": 4}, "baseStationName": {"type": "string", "id": 5}, "lon": {"type": "double", "id": 6}, "lat": {"type": "double", "id": 7}}}, "db_controller": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "orgId": {"type": "string", "id": 3}, "baseStationRid": {"type": "string", "id": 4}, "selfId": {"type": "string", "id": 5}, "dmrId": {"type": "string", "id": 6}, "lon": {"type": "double", "id": 7}, "lat": {"type": "double", "id": 8}, "note": {"type": "string", "id": 9}, "setting": {"type": "string", "id": 10}, "signalAera": {"type": "string", "id": 11}, "simInfo": {"type": "string", "id": 12}, "room": {"type": "string", "id": 13}, "sipNo": {"type": "string", "id": 14}, "netTimeSlot": {"type": "int32", "id": 15}, "controllerType": {"type": "int32", "id": 16}, "location": {"type": "string", "id": 17}, "canTalk": {"type": "bool", "id": 18}, "simulcastParent": {"type": "string", "id": 19}, "traditionalDmrAllowNetCall": {"type": "int32", "id": 20}}}, "db_controller_last_info": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 3}, "lastDataTime": {"type": "string", "id": 4}, "connected": {"type": "int32", "id": 5}}}, "db_controller_online_history": {"fields": {"rid": {"type": "string", "id": 1}, "controllerDmrId": {"type": "string", "id": 2}, "actionTime": {"type": "string", "id": 3}, "actionCode": {"type": "int32", "id": 4}, "note": {"type": "string", "id": 5}}}, "db_phone_gateway_filter": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "name": {"type": "string", "id": 3}, "lastModifyTime": {"type": "string", "id": 4}, "inBlack": {"type": "string", "id": 5}, "inBlackEnable": {"type": "bool", "id": 6}, "inWhite": {"type": "string", "id": 7}, "inWhiteEnable": {"type": "bool", "id": 8}, "outBlack": {"type": "string", "id": 9}, "outBlackEnable": {"type": "bool", "id": 10}, "outWhite": {"type": "string", "id": 11}, "outWhiteEnable": {"type": "bool", "id": 12}, "note": {"type": "string", "id": 13}, "setting": {"type": "string", "id": 14}}}, "db_device": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "orgId": {"type": "string", "id": 3}, "selfId": {"type": "string", "id": 4}, "dmrId": {"type": "string", "id": 5}, "virOrgs": {"type": "string", "id": 6}, "deviceUser": {"type": "string", "id": 7}, "note": {"type": "string", "id": 8}, "deviceType": {"type": "int32", "id": 9}, "channelLastModifyTime": {"type": "string", "id": 10}, "channel": {"type": "string", "id": 11}, "priority": {"type": "int32", "id": 12}, "gatewayFilterRid": {"type": "string", "id": 13}, "setting": {"type": "string", "id": 14}, "lastRfConfigTime": {"type": "string", "id": 15}, "lastRfWriteTime": {"type": "string", "id": 16}, "traditionalDmrAllowNetCall": {"type": "int32", "id": 17}, "devGroup": {"type": "string", "id": 18}, "pocSetting": {"type": "string", "id": 19}, "pocSettingLastModifyTime": {"type": "string", "id": 20}, "allowCallOffline": {"type": "int32", "id": 21}}}, "db_device_last_info": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "orgId": {"type": "string", "id": 3}, "dmrId": {"type": "string", "id": 5}, "lastDataTime": {"type": "string", "id": 6}, "lastRfidPerson": {"type": "string", "id": 7}, "lastRfidPersonTime": {"type": "string", "id": 4}, "lastRfid": {"type": "string", "id": 8}, "lastRfidTime": {"type": "string", "id": 9}, "lastGpsTime": {"type": "string", "id": 10}, "lastLon": {"type": "double", "id": 11}, "lastLat": {"type": "double", "id": 12}, "deviceLockState": {"type": "int32", "id": 13}, "msStatus": {"type": "string", "id": 14}, "lastController": {"type": "string", "id": 15}, "lastPoweronTime": {"type": "string", "id": 16}, "lastPoweroffTime": {"type": "string", "id": 17}, "lastGpsInvalidTime": {"type": "string", "id": 18}, "optStatus": {"type": "string", "id": 19}, "lastGpsSwitchState": {"type": "int32", "id": 20}}}, "db_user_title": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "titleName": {"type": "string", "id": 3}, "note": {"type": "string", "id": 4}, "titleSortValue": {"type": "int32", "id": 5}}}, "db_user": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "orgId": {"type": "string", "id": 3}, "selfId": {"type": "string", "id": 4}, "userName": {"type": "string", "id": 5}, "userTitle": {"type": "string", "id": 6}, "userPhone": {"type": "string", "id": 7}, "userImage": {"type": "string", "id": 8}, "userRfid": {"type": "string", "id": 9}, "userLoginName": {"type": "string", "id": 10}, "userLoginPass": {"type": "string", "id": 11}, "userSetting": {"type": "string", "id": 12}, "note": {"type": "string", "id": 13}, "allowLoginManage": {"type": "bool", "id": 14}, "creator": {"type": "string", "id": 15}, "isAdmin": {"type": "bool", "id": 16}, "isDispatcher": {"type": "bool", "id": 17}}}, "db_user_privelege": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "userRid": {"type": "string", "id": 3}, "userOrg": {"type": "string", "id": 4}, "includeChildren": {"type": "int32", "id": 5}}}, "db_user_session_id": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "userRid": {"type": "string", "id": 3}, "loginWay": {"type": "int32", "id": 4}, "sessionId": {"type": "string", "id": 5}, "effectiveTime": {"type": "string", "id": 6}}}, "db_virtual_org": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "orgId": {"type": "string", "id": 3}, "virtualUser": {"type": "string", "id": 4}}}, "db_map_point": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "orgId": {"type": "string", "id": 3}, "selfId": {"type": "string", "id": 4}, "pointName": {"type": "string", "id": 5}, "mapDisplayName": {"type": "string", "id": 6}, "note": {"type": "string", "id": 7}, "lon": {"type": "double", "id": 8}, "lat": {"type": "double", "id": 9}, "startShowLevel": {"type": "int32", "id": 10}, "colorR": {"type": "int32", "id": 11}, "colorG": {"type": "int32", "id": 12}, "colorB": {"type": "int32", "id": 13}, "pointImg": {"type": "string", "id": 14}, "imgOrColorPoint": {"type": "int32", "id": 15}, "markerWidth": {"type": "int32", "id": 16}, "markerHeight": {"type": "int32", "id": 17}, "markerType": {"type": "int32", "id": 18}}}, "db_line_point": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "orgId": {"type": "string", "id": 3}, "pointId": {"type": "string", "id": 4}, "pointName": {"type": "string", "id": 5}, "mapDisplayName": {"type": "string", "id": 6}, "note": {"type": "string", "id": 7}, "lon": {"type": "double", "id": 8}, "lat": {"type": "double", "id": 9}, "startShowLevel": {"type": "int32", "id": 10}, "colorR": {"type": "int32", "id": 11}, "colorG": {"type": "int32", "id": 12}, "colorB": {"type": "int32", "id": 13}, "pointImg": {"type": "string", "id": 14}, "imgOrColorPoint": {"type": "int32", "id": 15}, "pointType": {"type": "int32", "id": 16}, "pointRfid": {"type": "string", "id": 17}, "gpsPointRadius": {"type": "int32", "id": 18}, "lastLpalarmTime": {"type": "string", "id": 19}, "lastLpalarmState": {"type": "bool", "id": 20}}}, "db_line_point_latest_info": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "orgId": {"type": "string", "id": 3}, "lastCheckTime": {"type": "string", "id": 8}, "lastCheckDeviceId": {"type": "string", "id": 9}, "lastCheckUserId": {"type": "string", "id": 11}}}, "db_line_master": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "orgId": {"type": "string", "id": 3}, "lineId": {"type": "string", "id": 4}, "lineName": {"type": "string", "id": 5}, "note": {"type": "string", "id": 6}, "pointCount": {"type": "int32", "id": 7}, "lineDetailModify": {"type": "int32", "id": 8}}}, "db_line_detail": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "lineId": {"type": "string", "id": 3}, "pointId": {"type": "string", "id": 4}, "pointNo": {"type": "int32", "id": 5}, "aheadTime": {"type": "int32", "id": 6}, "delayTime": {"type": "int32", "id": 7}}}, "db_rfid_rule_master": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "orgId": {"type": "string", "id": 3}, "ruleId": {"type": "string", "id": 4}, "ruleName": {"type": "string", "id": 5}, "note": {"type": "string", "id": 6}, "day_1": {"type": "bool", "id": 7}, "day_2": {"type": "bool", "id": 8}, "day_3": {"type": "bool", "id": 9}, "day_4": {"type": "bool", "id": 10}, "day_5": {"type": "bool", "id": 11}, "day_6": {"type": "bool", "id": 12}, "day_7": {"type": "bool", "id": 13}, "checkStartTime": {"type": "string", "id": 14}, "checkAllTime": {"type": "int32", "id": 15}, "checkCount": {"type": "int32", "id": 16}, "ruleEffectiveType": {"type": "int32", "id": 17}, "ruleEffectiveStart": {"type": "string", "id": 18}, "ruleEffectiveEnd": {"type": "string", "id": 19}, "ruleLineRid": {"type": "string", "id": 20}}}, "db_device_power_onoff": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 3}, "deviceId": {"type": "string", "id": 4}, "userId": {"type": "string", "id": 6}, "actionTime": {"type": "string", "id": 8}, "actionType": {"type": "int32", "id": 9}}}, "db_user_check_in_history": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 3}, "deviceId": {"type": "string", "id": 4}, "userId": {"type": "string", "id": 6}, "actionTime": {"type": "string", "id": 8}, "actionType": {"type": "int32", "id": 9}, "rfidId": {"type": "string", "id": 10}}}, "db_rfid_history": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "checkTime": {"type": "string", "id": 3}, "checkerId": {"type": "string", "id": 4}, "devType": {"type": "int32", "id": 5}, "deviceId": {"type": "string", "id": 6}, "receiveTime": {"type": "string", "id": 8}, "receiver": {"type": "string", "id": 9}, "pointId": {"type": "string", "id": 10}}}, "db_gps_history": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "gpsTime": {"type": "string", "id": 3}, "gpsFixed": {"type": "bool", "id": 4}, "lon": {"type": "double", "id": 5}, "lat": {"type": "double", "id": 6}, "speed": {"type": "double", "id": 7}, "direction": {"type": "int32", "id": 8}, "altitude": {"type": "int32", "id": 9}, "deviceId": {"type": "string", "id": 10}, "personId": {"type": "string", "id": 11}, "deviceStatus": {"type": "string", "id": 12}, "upCmd": {"type": "string", "id": 13}}}, "db_alarm_history": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "alarmTime": {"type": "string", "id": 3}, "deviceId": {"type": "string", "id": 4}, "alarmDevType": {"type": "int32", "id": 5}, "personId": {"type": "string", "id": 6}, "deallerRid": {"type": "string", "id": 8}, "deallerTime": {"type": "string", "id": 9}, "deallerResult": {"type": "string", "id": 10}, "alarmType": {"type": "string", "id": 11}}}, "db_sound_history": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "deviceId": {"type": "string", "id": 3}, "sourceInfo": {"type": "string", "id": 4}, "soundTime": {"type": "string", "id": 5}, "soundLen": {"type": "int32", "id": 6}, "channel": {"type": "int32", "id": 7}, "controller": {"type": "string", "id": 8}, "fileName": {"type": "string", "id": 9}, "personId": {"type": "string", "id": 10}, "target": {"type": "string", "id": 11}, "targetInfo": {"type": "string", "id": 12}}}, "db_not_send_cmd": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "scheduleTime": {"type": "string", "id": 6}, "stopTime": {"type": "string", "id": 7}}}, "db_sent_cmd_history": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "inputTime": {"type": "string", "id": 3}, "inputUserId": {"type": "string", "id": 4}, "senderType": {"type": "int32", "id": 5}, "scheduleTime": {"type": "string", "id": 6}, "stopTime": {"type": "string", "id": 7}, "cmdTarget": {"type": "string", "id": 8}, "cmdTargetConSeq": {"type": "string", "id": 9}, "sendCmd": {"type": "string", "id": 10}, "origCbxx": {"type": "string", "id": 11}, "cmdParams": {"type": "string", "id": 12}, "sendTimeList": {"type": "string", "id": 13}}}, "db_device_register_info": {"fields": {"rid": {"type": "string", "id": 1}, "receiveTime": {"type": "string", "id": 3}, "conCh": {"type": "string", "id": 4}, "dmrId": {"type": "string", "id": 5}, "sellerId": {"type": "string", "id": 6}, "sn": {"type": "string", "id": 7}}}, "db_call_dispatch_history": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "actionTime": {"type": "string", "id": 3}, "personId": {"type": "string", "id": 4}, "deviceId": {"type": "string", "id": 6}, "controllerDmrid": {"type": "string", "id": 7}, "dispatchTargetDmrid": {"type": "string", "id": 8}, "dispatchCode": {"type": "int32", "id": 10}, "dispatchType": {"type": "int32", "id": 11}, "targetChannel": {"type": "int32", "id": 12}}}, "db_conf_dispatch_history": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "startTime": {"type": "string", "id": 3}, "startPersonRid": {"type": "string", "id": 5}, "controllerIds": {"type": "string", "id": 6}, "conferenceNo": {"type": "string", "id": 7}, "endOrgId": {"type": "string", "id": 8}, "endTime": {"type": "string", "id": 9}, "endPersonRid": {"type": "string", "id": 10}, "dispatchCode": {"type": "int32", "id": 11}}}, "db_not_confirm_sms": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "startTime": {"type": "string", "id": 3}, "senderDmrid": {"type": "string", "id": 4}, "targetDmrid": {"type": "string", "id": 5}, "receiveRepeater": {"type": "string", "id": 6}, "smsContent": {"type": "string", "id": 7}, "smsNo": {"type": "string", "id": 8}, "senderUserRid": {"type": "string", "id": 9}, "note": {"type": "string", "id": 10}, "smsType": {"type": "string", "id": 11}}}, "db_sms_history": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "startTime": {"type": "string", "id": 3}, "senderDmrid": {"type": "string", "id": 4}, "targetDmrid": {"type": "string", "id": 5}, "receiveRepeater": {"type": "string", "id": 6}, "smsContent": {"type": "string", "id": 7}, "smsNo": {"type": "string", "id": 8}, "confirmTime": {"type": "string", "id": 9}, "note": {"type": "string", "id": 10}, "senderUserRid": {"type": "string", "id": 11}, "smsType": {"type": "string", "id": 12}}}, "db_ch_rf_setting": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "name": {"type": "string", "id": 3}, "rfSetting": {"type": "string", "id": 4}, "settings": {"type": "string", "id": 14}}}, "db_device_setting_conf": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "confName": {"type": "string", "id": 3}, "lastModifyTime": {"type": "string", "id": 4}, "userName": {"type": "string", "id": 5}, "conf": {"type": "string", "id": 14}}}, "db_phone_short_no": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "shortNo": {"type": "string", "id": 3}, "lastModifyTime": {"type": "string", "id": 4}, "refOrgId": {"type": "string", "id": 5}, "refDevId": {"type": "string", "id": 6}, "note": {"type": "string", "id": 8}, "setting": {"type": "string", "id": 14}}}, "db_phone_gateway_permission": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "name": {"type": "string", "id": 3}, "lastModifyTime": {"type": "string", "id": 4}, "permOrgId": {"type": "string", "id": 5}, "permDevId": {"type": "string", "id": 6}, "gatewayRid": {"type": "string", "id": 7}, "note": {"type": "string", "id": 8}, "setting": {"type": "string", "id": 14}}}, "db_controller_gateway_manage": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "refControllerId": {"type": "string", "id": 3}, "phonePos": {"type": "int32", "id": 4}, "phoneNo": {"type": "string", "id": 5}, "refDevId": {"type": "string", "id": 6}}}, "db_phone_no_list": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "lastModifyTime": {"type": "string", "id": 4}, "phoneName": {"type": "string", "id": 7}, "phoneNo": {"type": "string", "id": 8}, "setting": {"type": "string", "id": 14}}}, "db_linepoint_alarm_history": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "checkTime": {"type": "string", "id": 3}, "checkerId": {"type": "string", "id": 4}, "deviceId": {"type": "string", "id": 6}, "receiveTime": {"type": "string", "id": 8}, "receiver": {"type": "string", "id": 9}, "pointId": {"type": "string", "id": 10}, "alarmCode": {"type": "int32", "id": 11}}}, "db_device_channel_zone": {"fields": {"rid": {"type": "string", "id": 1}, "zoneLevel": {"type": "int32", "id": 2}, "zoneNo": {"type": "int32", "id": 3}, "zoneTitle": {"type": "string", "id": 4}, "zoneParent": {"type": "string", "id": 5}, "setting": {"type": "string", "id": 6}}}, "db_crud_log": {"fields": {"rid": {"type": "string", "id": 1}, "orgRid": {"type": "string", "id": 2}, "userRid": {"type": "string", "id": 3}, "operation": {"type": "string", "id": 4}, "req": {"type": "string", "id": 5}, "reqOption": {"type": "string", "id": 6}, "ipInfo": {"type": "string", "id": 7}, "note": {"type": "string", "id": 8}, "updateAt": {"type": "string", "id": 14}}}, "db_dynamic_group_detail": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "deviceRid": {"type": "string", "id": 3}, "deviceDmrid": {"type": "string", "id": 4}, "groupRid": {"type": "string", "id": 5}, "groupDmrid": {"type": "string", "id": 6}, "isDeviceGroup": {"type": "int32", "id": 7}, "memberState": {"type": "int32", "id": 8}, "dynamicGroupType": {"type": "int32", "id": 9}, "memberOrgId": {"type": "string", "id": 10}, "dynamicGroupState": {"type": "int32", "id": 11}, "taskConfirmTime": {"type": "string", "id": 12}, "creator": {"type": "string", "id": 13}, "createTime": {"type": "string", "id": 15}}}, "db_iot_device": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "devId": {"type": "string", "id": 3}, "devType": {"type": "sint32", "id": 4}, "devName": {"type": "string", "id": 6}, "note": {"type": "string", "id": 7}, "lon": {"type": "double", "id": 8}, "lat": {"type": "double", "id": 9}, "setting": {"type": "string", "id": 10}, "creator": {"type": "string", "id": 13}, "createTime": {"type": "string", "id": 15}}}, "db_iot_restriction": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "iotId": {"type": "string", "id": 3}, "restrictType": {"type": "int32", "id": 4}, "restrictStationRid": {"type": "string", "id": 5}, "restrictAliveInterval": {"type": "sint32", "id": 6}, "creator": {"type": "string", "id": 13}, "createTime": {"type": "string", "id": 15}}}, "db_iot_device_last_info": {"fields": {"rid": {"type": "string", "id": 1}, "updateAt": {"type": "string", "id": 2}, "orgId": {"type": "string", "id": 3}, "devId": {"type": "string", "id": 5}, "lastDataTime": {"type": "string", "id": 6}, "lastCmd": {"type": "sint32", "id": 7}, "devStatus": {"type": "string", "id": 14}, "lastController": {"type": "string", "id": 15}, "optStatus": {"type": "string", "id": 19}}}, "db_iot_data_history": {"fields": {"rid": {"type": "string", "id": 1}, "orgId": {"type": "string", "id": 2}, "cmdTime": {"type": "string", "id": 3}, "cmd": {"type": "sint32", "id": 4}, "devType": {"type": "int32", "id": 5}, "devId": {"type": "string", "id": 6}, "recvStationId": {"type": "string", "id": 7}, "recvTime": {"type": "string", "id": 8}, "receiver": {"type": "string", "id": 9}, "cmdParam": {"type": "string", "id": 10}}}, "db_static_subscribes": {"fields": {"rid": {"type": "string", "id": 1}, "controllerDmrId": {"type": "string", "id": 2}, "groupDmrId": {"type": "string", "id": 3}, "creator": {"type": "string", "id": 13}, "createTime": {"type": "string", "id": 15}}}, "db_app_map_privilege_device": {"fields": {"rid": {"type": "string", "id": 1}, "appDmrid": {"type": "string", "id": 2}, "grantDeviceDmrid": {"type": "string", "id": 3}, "grantUserRid": {"type": "string", "id": 4}, "expireTime": {"type": "string", "id": 5}, "isSetExpire": {"type": "int32", "id": 6}, "applyTime": {"type": "string", "id": 7}, "grantTime": {"type": "string", "id": 8}, "grantUserName": {"type": "string", "id": 9}}}, "db_poc_session": {"fields": {"rid": {"type": "string", "id": 1}, "pocDmrid": {"type": "string", "id": 2}, "sessionId": {"type": "string", "id": 3}, "loginTime": {"type": "string", "id": 4}, "ipInfo": {"type": "string", "id": 5}, "lastUpdateTime": {"type": "string", "id": 6}}}, "db_group_call_contacts": {"fields": {"rid": {"type": "string", "id": 1}, "userRid": {"type": "string", "id": 2}, "groupDmrid": {"type": "string", "id": 3}, "sortValue": {"type": "int32", "id": 4}, "creatorRid": {"type": "string", "id": 5}, "createTime": {"type": "string", "id": 6}}}, "db_single_call_contacts": {"fields": {"rid": {"type": "string", "id": 1}, "userRid": {"type": "string", "id": 2}, "singleDmrid": {"type": "string", "id": 3}, "sortValue": {"type": "int32", "id": 4}, "creatorRid": {"type": "string", "id": 5}, "createTime": {"type": "string", "id": 6}}}, "request_command": {"values": {"UNKNOW": 0, "CLIENT_LOGIN_REQUEST": 10, "CLIENT_LOGIN_RESPONSE": 11, "DB_ORG_OPERATION": 20, "DB_POSITION_OPERATION": 22}}, "rpc_cmd": {"fields": {"reqId": {"type": "string", "id": 1}, "seqNo": {"type": "int32", "id": 2}, "sysId": {"type": "string", "id": 3}, "sid": {"type": "string", "id": 4}, "reqOrResponse": {"type": "int32", "id": 5}, "origReqId": {"type": "string", "id": 6}, "resInfo": {"type": "string", "id": 7}, "command": {"type": "int32", "id": 8}, "compressMethod": {"type": "int32", "id": 9}, "body": {"type": "bytes", "id": 10}, "opt": {"type": "string", "id": 11}, "optInt": {"type": "int64", "id": 12}}}, "bfdx": {"methods": {"server_call": {"requestType": "rpc_cmd", "responseType": "rpc_cmd"}}}, "db_sys_config_list": {"fields": {"rows": {"rule": "repeated", "type": "db_sys_config", "id": 1}}}, "db_table_operate_time_list": {"fields": {"rows": {"rule": "repeated", "type": "db_table_operate_time", "id": 1}}}, "db_org_list": {"fields": {"rows": {"rule": "repeated", "type": "db_org", "id": 1}}}, "db_image_list": {"fields": {"rows": {"rule": "repeated", "type": "db_image", "id": 1}}}, "db_base_station_list": {"fields": {"rows": {"rule": "repeated", "type": "db_base_station", "id": 1}}}, "db_controller_list": {"fields": {"rows": {"rule": "repeated", "type": "db_controller", "id": 1}}}, "db_controller_last_info_list": {"fields": {"rows": {"rule": "repeated", "type": "db_controller_last_info", "id": 1}}}, "db_controller_online_history_list": {"fields": {"rows": {"rule": "repeated", "type": "db_controller_online_history", "id": 1}}}, "db_phone_gateway_filter_list": {"fields": {"rows": {"rule": "repeated", "type": "db_phone_gateway_filter", "id": 1}}}, "db_device_list": {"fields": {"rows": {"rule": "repeated", "type": "db_device", "id": 1}}}, "db_device_last_info_list": {"fields": {"rows": {"rule": "repeated", "type": "db_device_last_info", "id": 1}}}, "db_user_title_list": {"fields": {"rows": {"rule": "repeated", "type": "db_user_title", "id": 1}}}, "db_user_list": {"fields": {"rows": {"rule": "repeated", "type": "db_user", "id": 1}}}, "db_user_privelege_list": {"fields": {"rows": {"rule": "repeated", "type": "db_user_privelege", "id": 1}}}, "db_user_session_id_list": {"fields": {"rows": {"rule": "repeated", "type": "db_user_session_id", "id": 1}}}, "db_virtual_org_list": {"fields": {"rows": {"rule": "repeated", "type": "db_virtual_org", "id": 1}}}, "db_map_point_list": {"fields": {"rows": {"rule": "repeated", "type": "db_map_point", "id": 1}}}, "db_line_point_list": {"fields": {"rows": {"rule": "repeated", "type": "db_line_point", "id": 1}}}, "db_line_point_latest_info_list": {"fields": {"rows": {"rule": "repeated", "type": "db_line_point_latest_info", "id": 1}}}, "db_line_master_list": {"fields": {"rows": {"rule": "repeated", "type": "db_line_master", "id": 1}}}, "db_line_detail_list": {"fields": {"rows": {"rule": "repeated", "type": "db_line_detail", "id": 1}}}, "db_rfid_rule_master_list": {"fields": {"rows": {"rule": "repeated", "type": "db_rfid_rule_master", "id": 1}}}, "db_device_power_onoff_list": {"fields": {"rows": {"rule": "repeated", "type": "db_device_power_onoff", "id": 1}}}, "db_user_check_in_history_list": {"fields": {"rows": {"rule": "repeated", "type": "db_user_check_in_history", "id": 1}}}, "db_rfid_history_list": {"fields": {"rows": {"rule": "repeated", "type": "db_rfid_history", "id": 1}}}, "db_gps_history_list": {"fields": {"rows": {"rule": "repeated", "type": "db_gps_history", "id": 1}}}, "db_alarm_history_list": {"fields": {"rows": {"rule": "repeated", "type": "db_alarm_history", "id": 1}}}, "db_sound_history_list": {"fields": {"rows": {"rule": "repeated", "type": "db_sound_history", "id": 1}}}, "db_not_send_cmd_list": {"fields": {"rows": {"rule": "repeated", "type": "db_not_send_cmd", "id": 1}}}, "db_sent_cmd_history_list": {"fields": {"rows": {"rule": "repeated", "type": "db_sent_cmd_history", "id": 1}}}, "db_device_register_info_list": {"fields": {"rows": {"rule": "repeated", "type": "db_device_register_info", "id": 1}}}, "db_call_dispatch_history_list": {"fields": {"rows": {"rule": "repeated", "type": "db_call_dispatch_history", "id": 1}}}, "db_conf_dispatch_history_list": {"fields": {"rows": {"rule": "repeated", "type": "db_conf_dispatch_history", "id": 1}}}, "db_not_confirm_sms_list": {"fields": {"rows": {"rule": "repeated", "type": "db_not_confirm_sms", "id": 1}}}, "db_sms_history_list": {"fields": {"rows": {"rule": "repeated", "type": "db_sms_history", "id": 1}}}, "db_ch_rf_setting_list": {"fields": {"rows": {"rule": "repeated", "type": "db_ch_rf_setting", "id": 1}}}, "db_device_setting_conf_list": {"fields": {"rows": {"rule": "repeated", "type": "db_device_setting_conf", "id": 1}}}, "db_phone_short_no_list": {"fields": {"rows": {"rule": "repeated", "type": "db_phone_short_no", "id": 1}}}, "db_phone_gateway_permission_list": {"fields": {"rows": {"rule": "repeated", "type": "db_phone_gateway_permission", "id": 1}}}, "db_controller_gateway_manage_list": {"fields": {"rows": {"rule": "repeated", "type": "db_controller_gateway_manage", "id": 1}}}, "db_phone_no_list_list": {"fields": {"rows": {"rule": "repeated", "type": "db_phone_no_list", "id": 1}}}, "db_linepoint_alarm_history_list": {"fields": {"rows": {"rule": "repeated", "type": "db_linepoint_alarm_history", "id": 1}}}, "db_device_channel_zone_list": {"fields": {"rows": {"rule": "repeated", "type": "db_device_channel_zone", "id": 1}}}, "db_crud_log_list": {"fields": {"rows": {"rule": "repeated", "type": "db_crud_log", "id": 1}}}, "db_dynamic_group_detail_list": {"fields": {"rows": {"rule": "repeated", "type": "db_dynamic_group_detail", "id": 1}}}, "db_iot_device_list": {"fields": {"rows": {"rule": "repeated", "type": "db_iot_device", "id": 1}}}, "db_iot_restriction_list": {"fields": {"rows": {"rule": "repeated", "type": "db_iot_restriction", "id": 1}}}, "db_iot_device_last_info_list": {"fields": {"rows": {"rule": "repeated", "type": "db_iot_device_last_info", "id": 1}}}, "db_iot_data_history_list": {"fields": {"rows": {"rule": "repeated", "type": "db_iot_data_history", "id": 1}}}, "db_static_subscribes_list": {"fields": {"rows": {"rule": "repeated", "type": "db_static_subscribes", "id": 1}}}, "db_app_map_privilege_device_list": {"fields": {"rows": {"rule": "repeated", "type": "db_app_map_privilege_device", "id": 1}}}, "db_poc_session_list": {"fields": {"rows": {"rule": "repeated", "type": "db_poc_session", "id": 1}}}, "cpu_info": {"fields": {"brandName": {"type": "string", "id": 1}, "vendor": {"type": "string", "id": 2}, "physicalCores": {"type": "int32", "id": 3}, "logicalCores": {"type": "int32", "id": 4}}}, "sys_info": {"fields": {"bfSwName": {"type": "string", "id": 1}, "bfSwVersion": {"type": "string", "id": 2}, "cpuinfo": {"type": "cpu_info", "id": 3}, "memory": {"type": "uint64", "id": 4}, "macs": {"rule": "repeated", "type": "bytes", "id": 5}}}, "lic_8100_content": {"fields": {"modRfid": {"type": "uint32", "id": 1}, "modRecord": {"type": "uint32", "id": 2}, "modPhoneGateway": {"type": "uint32", "id": 3}, "modDispatch": {"type": "uint32", "id": 4}, "modTraditionalDmr": {"type": "uint32", "id": 5}, "maxDevices": {"type": "uint32", "id": 6}, "maxUsers": {"type": "uint32", "id": 7}, "maxControllers": {"type": "uint32", "id": 8}, "modSvt": {"type": "uint32", "id": 9}}}, "lic_request": {"fields": {"sysinfo": {"type": "sys_info", "id": 1}, "projName": {"type": "string", "id": 2}, "requestTime": {"type": "string", "id": 3}, "firstRunTime": {"type": "int64", "id": 4}, "systemCode": {"type": "uint32", "id": 5}, "userLicContent": {"type": "bytes", "id": 6}}}, "lic_request_file": {"fields": {"projName": {"type": "string", "id": 2}, "keyPrefix": {"type": "string", "id": 3}, "note": {"type": "string", "id": 6}, "data": {"type": "string", "id": 8}, "dataVer": {"type": "string", "id": 9}}}, "lic_content": {"fields": {"bfSwName": {"type": "string", "id": 1}, "licenses": {"keyType": "string", "type": "uint32", "id": 2}, "expireTime": {"type": "string", "id": 3}}}, "lic_response": {"fields": {"sysinfo": {"type": "sys_info", "id": 1}, "projName": {"type": "string", "id": 2}, "lic": {"type": "lic_content", "id": 3}, "issueTime": {"type": "string", "id": 6}}}, "lic_response_file": {"fields": {"projName": {"type": "string", "id": 2}, "keyPrefix": {"type": "string", "id": 3}, "issueTime": {"type": "string", "id": 6}, "data": {"type": "string", "id": 8}, "sign": {"type": "string", "id": 9}}}, "MessageVersion": {"values": {"MV_Unknow": 0, "MV_VERSION_1": 1}}, "ServiceType": {"values": {"ST_Unknow": 0, "ST_SG_CALL": 1}}, "MessageType": {"values": {"MT_Unknow": 0, "MT_SG_LOGIN_REQUEST": 2000, "MT_GS_LOGIN_RESPONSE": 2001, "MT_SG_HEARTBEAT_REPORT": 2002, "MT_GS_HEARTBEAT_CONFIRM": 2003, "MT_SG_GROUP_LIST": 2004, "MT_SG_MS_LIST": 2005, "MT_SG_MS_ONLINE_STATUS_UPDATE": 2006, "MT_SG_MS_UPDATE": 2007, "MT_SG_GROUP_UPDATE": 2008, "MT_SG_GET_MS_LIST_REQUEST": 2009, "MT_GS_GROUP_TYPE_UPDATE": 2010, "MT_SG_CALLING_START": 2011, "MT_SG_CALLING_STOP": 2012, "MT_SG_CALLED_STOP": 2013, "MT_SG_LOCATION_REPORT": 2014, "MT_SG_SMS_NOTIFY": 2015, "MT_GS_MS_LIST": 2016, "MT_GS_MS_UPDATE": 2017, "MT_GS_CREATE_TEMP_GROUP": 2018, "MT_GS_TEMP_GROUP_UPDATE_MEMBER_REQUEST": 2019, "MT_SG_TEMP_GROUP_MEMBER_UPDATE": 2020, "MT_GS_DELETE_TEMP_GROUP": 2021, "MT_SG_GET_MS_ONLINE_STATUS_REQUEST": 2022}}, "MTSG_LoginRequest": {"fields": {"loginName": {"type": "string", "id": 1}, "authRandom": {"type": "bytes", "id": 2}, "authCode": {"type": "bytes", "id": 3}}}, "MTGS_LoginResponse": {"fields": {"loginName": {"type": "string", "id": 1}, "result": {"type": "int32", "id": 2}, "gwID": {"type": "uint32", "id": 3}, "receiveIP": {"type": "string", "id": 4}, "receivePort": {"type": "uint32", "id": 5}, "random": {"type": "uint32", "id": 6}}}, "MTSG_HeartbeatReport": {"fields": {"gwID": {"type": "uint32", "id": 1}, "attachData": {"type": "bytes", "id": 2}}}, "MTGS_HeartbeatConfirm": {"fields": {"gwID": {"type": "uint32", "id": 1}, "attachData": {"type": "bytes", "id": 2}}}, "MTSG_GroupList": {"fields": {"gwID": {"type": "uint32", "id": 1}, "listEnd": {"type": "uint32", "id": 2}, "groupList": {"rule": "repeated", "type": "db_org", "id": 3}}}, "MTSG_MsList": {"fields": {"gwID": {"type": "uint32", "id": 1}, "listEnd": {"type": "uint32", "id": 2}, "msList": {"rule": "repeated", "type": "db_device", "id": 3}}}, "MsStatusInfo": {"fields": {"msID": {"type": "string", "id": 1}, "onlineStatus": {"type": "uint32", "id": 2}}}, "MTSG_MsOnlineStateUpdate": {"fields": {"gwID": {"type": "uint32", "id": 1}, "msStatusList": {"rule": "repeated", "type": "MsStatusInfo", "id": 2}}}, "MTSG_GetMsOnlineStatusRequest": {"fields": {"gwID": {"type": "uint32", "id": 1}, "getAll": {"type": "uint32", "id": 2}, "msIDList": {"rule": "repeated", "type": "string", "id": 3}}}, "MTSG_MsUpdate": {"fields": {"gwID": {"type": "uint32", "id": 1}, "operate": {"type": "uint32", "id": 2}, "msID": {"type": "string", "id": 3}, "msInfo": {"type": "db_device", "id": 4}}}, "MTSG_GroupUpdate": {"fields": {"gwID": {"type": "uint32", "id": 1}, "operate": {"type": "uint32", "id": 2}, "groupID": {"type": "string", "id": 3}, "groupInfo": {"type": "db_org", "id": 4}}}, "MTSG_GetMsListRequest": {"fields": {"gwID": {"type": "uint32", "id": 1}}}, "GroupTypeInfo": {"fields": {"groupID": {"type": "string", "id": 1}, "groupType": {"type": "uint32", "id": 2}}}, "MTGS_GroupTypeUpdate": {"fields": {"gwID": {"type": "uint32", "id": 1}, "groupList": {"rule": "repeated", "type": "GroupTypeInfo", "id": 2}}}, "MTSG_CallingStart": {"fields": {"gwID": {"type": "uint32", "id": 1}, "callType": {"type": "uint32", "id": 2}, "callingMsID": {"type": "string", "id": 3}, "callingMsName": {"type": "string", "id": 4}, "calledTargetID": {"type": "string", "id": 5}, "priority": {"type": "uint32", "id": 6}, "callingSeq": {"type": "uint32", "id": 7}}}, "MTSG_CallingStop": {"fields": {"gwID": {"type": "uint32", "id": 1}, "callType": {"type": "uint32", "id": 2}, "callingMsID": {"type": "string", "id": 3}, "calledTargetID": {"type": "string", "id": 4}}}, "MTSG_CalledStop": {"fields": {"gwID": {"type": "uint32", "id": 1}, "callingMsID": {"type": "string", "id": 2}, "stopReason": {"type": "uint32", "id": 3}}}, "LocationInfo": {"fields": {"msID": {"type": "string", "id": 1}, "coordinateSystem": {"type": "uint32", "id": 2}, "latitude": {"type": "double", "id": 3}, "longitude": {"type": "double", "id": 4}, "speed": {"type": "double", "id": 5}, "altitude": {"type": "double", "id": 6}, "direction": {"type": "uint32", "id": 7}, "time": {"type": "uint64", "id": 8}}}, "MTSG_LocationReport": {"fields": {"gwID": {"type": "uint32", "id": 1}, "locationList": {"rule": "repeated", "type": "LocationInfo", "id": 2}}}, "TargetInfo": {"fields": {"targetType": {"type": "uint32", "id": 1}, "targetID": {"type": "string", "id": 2}}}, "SMSInfo": {"fields": {"sendMsID": {"type": "string", "id": 1}, "targetList": {"rule": "repeated", "type": "TargetInfo", "id": 2}, "content": {"type": "string", "id": 3}, "time": {"type": "uint64", "id": 4}}}, "MTSG_SMSNotify": {"fields": {"gwID": {"type": "uint32", "id": 1}, "smsList": {"rule": "repeated", "type": "SMSInfo", "id": 2}}}, "MsInfo": {"fields": {"msID": {"type": "uint32", "id": 1}, "msType": {"type": "uint32", "id": 2}, "deviceId": {"type": "string", "id": 3}, "priority": {"type": "uint32", "id": 4}}}, "MTGS_MsList": {"fields": {"gwID": {"type": "uint32", "id": 1}, "listEnd": {"type": "uint32", "id": 2}, "msList": {"rule": "repeated", "type": "MsInfo", "id": 3}}}, "MTGS_MsUpdate": {"fields": {"gwID": {"type": "uint32", "id": 1}, "operate": {"type": "uint32", "id": 2}, "msID": {"type": "string", "id": 3}, "msInfo": {"type": "MsInfo", "id": 4}}}, "MTGS_CreateTempGroup": {"fields": {"gwID": {"type": "uint32", "id": 1}, "groupID": {"type": "uint32", "id": 2}, "groupName": {"type": "string", "id": 3}, "memberList": {"rule": "repeated", "type": "string", "id": 4}}}, "MTGS_TempGroupUpdateMemberRequest": {"fields": {"gwID": {"type": "uint32", "id": 1}, "groupID": {"type": "uint32", "id": 2}, "operate": {"type": "uint32", "id": 3}, "memberList": {"rule": "repeated", "type": "string", "id": 4}}}, "MTSG_TempGroupMemberUpdate": {"fields": {"gwID": {"type": "uint32", "id": 1}, "groupID": {"type": "uint32", "id": 2}, "memberList": {"rule": "repeated", "type": "db_dynamic_group_detail", "id": 3}}}, "MTGS_DeleteTempGroup": {"fields": {"gwID": {"type": "uint32", "id": 1}, "groupID": {"type": "uint32", "id": 2}}}, "CommonReq": {"fields": {"Sid": {"type": "string", "id": 1}, "StrData": {"type": "string", "id": 2}, "IntData": {"type": "fixed32", "id": 3}, "BinData": {"type": "bytes", "id": 4}}}, "RespCode": {"values": {"RespCode_Unused": 0, "RespCode_OK": 1, "RespCode_Err": 2, "RespCode_Bad_Request": 3, "RespCode_Busy": 4, "RespCode_Cant_Match_Board": 5, "RespCode_Not_Found": 6}}, "CommonResp": {"fields": {"Code": {"type": "RespCode", "id": 1}, "RespInfo": {"type": "string", "id": 2}, "StrData": {"type": "string", "id": 3}, "IntData": {"type": "fixed32", "id": 4}, "BinData": {"type": "bytes", "id": 5}}}, "Gateway8100SubsInfo": {"fields": {"DMRIDs": {"rule": "repeated", "type": "fixed32", "id": 1}}}, "Prochat": {"methods": {"QueryProchatDeviceList": {"requestType": "CommonReq", "responseType": "CommonResp"}}}}}, "tr925": {"options": {"optimize_for": "LITE_RUNTIME"}, "nested": {"RepeaterOperation": {"values": {"None": 0, "Query": 5, "InsertUpdate": 6, "Delete": 7, "Restart": 8, "PowerRestart": 9, "RepeaterCurChInfo": 10, "RepeaterCurChSet": 11, "RepeaterCurPowerSet": 12, "RepeaterCurChChange": 13}}, "TableId": {"values": {"None": 0, "RepeaterInfo": 1, "RepeaterCommonSetting": 2, "RepeaterKeyFunctionSetting": 3, "RepeaterMenuSetting": 4, "RepeaterPresetSms": 5, "RepeaterPositionSetting": 6, "RepeaterBdSetting": 7, "RepeaterBdContact": 8, "RepeaterIpSetting": 9, "RepeaterServerSetting": 10, "RepeaterDmrSetting": 13, "RepeaterDmrContact": 14, "RepeaterDmrContactGroup": 15, "RepeaterMainZone": 16, "RepeaterSubZone": 17, "RepeaterUserZone": 18, "RepeaterOneChannel": 19}}, "RepeatorOperationInfo": {"fields": {"operation": {"type": "RepeaterOperation", "id": 1}, "tableId": {"type": "TableId", "id": 2}, "objIndex": {"type": "int32", "id": 3}, "objNum": {"type": "int32", "id": 4}, "zoneId": {"type": "ZoneId", "id": 5}}}, "ZoneId": {"fields": {"mainZoneId": {"type": "int32", "id": 1}, "subZoneId": {"type": "int32", "id": 2}, "userZoneId": {"type": "int32", "id": 3}}}, "RepeaterCurChInfo": {"fields": {"zoneId": {"type": "ZoneId", "id": 1}, "channelInfo": {"type": "RepeaterOneChannel", "id": 2}, "curstomPowerLv": {"type": "int32", "id": 3}}}, "RepeaterCurChSet": {"fields": {"zoneId": {"type": "ZoneId", "id": 1}, "chId": {"type": "int32", "id": 2}}}, "RepeaterCurPowerSet": {"fields": {"txPower": {"type": "int32", "id": 1}, "curstomPowerLv": {"type": "int32", "id": 2}, "customVehiclePlv": {"type": "int32", "id": 3}}}, "RepeaterInfo": {"fields": {"version": {"type": "string", "id": 1}, "lowFrequency": {"type": "fixed32", "id": 2}, "highFrequency": {"type": "fixed32", "id": 3}, "sn": {"type": "bytes", "id": 4}, "deviceModel": {"type": "string", "id": 5}}}, "RepeaterCommonSetting": {"fields": {"devName": {"type": "bytes", "id": 1}, "poweronPwdThreshold": {"type": "int32", "id": 2}, "poweronUiEnable": {"type": "int32", "id": 3}, "poweronPwd": {"type": "string", "id": 4}, "chHangTime": {"type": "int32", "id": 8}, "groupHangTime": {"type": "int32", "id": 9}, "praviteHangTime": {"type": "int32", "id": 10}, "emerHangTime": {"type": "int32", "id": 11}, "autoSignalDurtion": {"type": "int32", "id": 13}, "autoSignalInterval": {"type": "int32", "id": 14}, "analogHangTime": {"type": "int32", "id": 12}, "toneEnable": {"type": "int32", "id": 5}, "languageType": {"type": "int32", "id": 7}, "ledEnable": {"type": "int32", "id": 6}, "selectZoneCh": {"type": "int32", "id": 15}, "defaultZoneid": {"type": "ZoneId", "id": 16}, "defaultChannel": {"type": "int32", "id": 17}}}, "RepeaterKey": {"values": {"None": 0, "VolumeUp": 32, "VolumeDown": 33, "ChannelUp": 34, "ChannelDown": 35, "ChannelSetting": 48, "PowerSwitch": 49, "MonitorSwitch": 50, "AreaUp": 51, "AreaDown": 52, "SquelchSwitch": 53, "AddressBook": 54, "PreMadeSms": 55, "CallRecord": 56, "RootDirList": 57, "StandbyInterface": 64, "ErrorTestFire": 65, "ErrorTestReceive": 66}}, "RepeaterKeyFunctionSetting": {"fields": {"longPressTime": {"type": "int32", "id": 1}, "key_01ShortPressFunc": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 2}, "key_01LongPressFunc": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 3}, "key_02ShortPressFunc": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 4}, "key_02LongPressFunc": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 5}, "key_03ShortPressFunc": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 6}, "key_03LongPressFunc": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 7}, "key_04ShortPressFunc": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 8}, "key_04LongPressFunc": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 9}, "numKeyLongPressFunc": {"type": "bytes", "id": 10}}}, "RepeaterMenuSetting": {"fields": {"hangTime": {"type": "int32", "id": 1}, "setEnable": {"type": "int32", "id": 2}, "devSet": {"type": "int32", "id": 3}, "chSet": {"type": "int32", "id": 4}, "devInfoEnable": {"type": "int32", "id": 5}, "devInfo": {"type": "int32", "id": 6}, "zoneEnable": {"type": "int32", "id": 7}, "contactEnable": {"type": "int32", "id": 8}, "contactGroup": {"type": "int32", "id": 9}, "contactDmr": {"type": "int32", "id": 10}, "dialVisible": {"type": "int32", "id": 11}, "contactBd": {"type": "int32", "id": 12}, "smsEnable": {"type": "int32", "id": 13}, "smsDmr": {"type": "int32", "id": 14}, "smsBd": {"type": "int32", "id": 15}, "annexEnbale": {"type": "int32", "id": 16}, "additionBd": {"type": "int32", "id": 17}}}, "RepeaterPresetSms": {"fields": {"smsId": {"type": "int32", "id": 1}, "smsLen": {"type": "int32", "id": 2}, "smsContent": {"type": "bytes", "id": 3}}}, "RepeaterPositionSetting": {"fields": {"posEnable": {"type": "int32", "id": 1}, "timeZoneId": {"type": "int32", "id": 2}, "timeZoneSet": {"type": "int32", "id": 3}, "speedUnit": {"type": "int32", "id": 4}}}, "RepeaterBdSetting": {"fields": {"bdEnable": {"type": "int32", "id": 1}, "bdTargetId": {"type": "int32", "id": 2}, "bdGateId": {"type": "fixed32", "id": 3}, "remoteCtrlEnable": {"type": "int32", "id": 4}, "remoteUiPwd": {"type": "string", "id": 5}, "remoteDisCode": {"type": "string", "id": 6}, "remoteEnCode": {"type": "string", "id": 7}}}, "RepeaterBdContact": {"fields": {"indexId": {"type": "int32", "id": 1}, "sortId": {"type": "int32", "id": 2}, "bdNum": {"type": "fixed32", "id": 3}, "userName": {"type": "bytes", "id": 4}, "inUse": {"type": "int32", "id": 5}}}, "RepeaterIpSetting": {"fields": {"ipEnable": {"type": "int32", "id": 1}, "ipMode": {"type": "int32", "id": 2}, "ipAddr": {"type": "fixed32", "id": 3}, "ipMask": {"type": "fixed32", "id": 4}, "ipGateway": {"type": "fixed32", "id": 5}, "ipDns": {"type": "fixed32", "id": 6}}}, "RepeaterServerSetting": {"fields": {"serverAddr": {"type": "string", "id": 1}, "serverPort": {"type": "int32", "id": 2}, "localPort": {"type": "int32", "id": 3}}}, "RepeaterDmrSetting": {"fields": {"dmrid": {"type": "fixed32", "id": 1}, "repeatorId": {"type": "fixed32", "id": 2}, "priorityLevel": {"type": "int32", "id": 3}, "rciHoldTime": {"type": "int32", "id": 4}}}, "RepeaterDmrContact": {"fields": {"contactId": {"type": "int32", "id": 1}, "sortId": {"type": "int32", "id": 2}, "callerId": {"type": "fixed32", "id": 3}, "userName": {"type": "bytes", "id": 4}, "inUse": {"type": "int32", "id": 5}}}, "RepeaterDmrContactGroup": {"fields": {"groupId": {"type": "int32", "id": 1}, "userName": {"type": "bytes", "id": 2}, "contactArray": {"type": "bytes", "id": 3}}}, "RepeaterMainZone": {"fields": {"id": {"type": "int32", "id": 1}, "name": {"type": "bytes", "id": 2}}}, "RepeaterSubZone": {"fields": {"id": {"type": "int32", "id": 1}, "name": {"type": "bytes", "id": 2}, "mainId": {"type": "int32", "id": 3}}}, "RepeaterUserZone": {"fields": {"id": {"type": "int32", "id": 1}, "name": {"type": "bytes", "id": 2}, "chNum": {"type": "int32", "id": 3}, "subId": {"type": "int32", "id": 4}, "chId": {"type": "bytes", "id": 5}}}, "RepeaterOneChannel": {"fields": {"chId": {"type": "int32", "id": 1}, "chName": {"type": "bytes", "id": 2}, "chType": {"type": "int32", "id": 3}, "colourCodes": {"type": "int32", "id": 4}, "rxFrequency": {"type": "fixed32", "id": 5}, "txFrequency": {"type": "fixed32", "id": 6}, "txPower": {"type": "int32", "id": 7}}}, "SimulateDevice": {"fields": {"dmrid": {"type": "fixed32", "id": 1}, "currentCh": {"type": "int32", "id": 2}, "action": {"type": "int32", "id": 3}}}}}}}