<template>
  <el-container class="common-bg w-full h-full !flex-col app-layout">
    <!-- 顶部导航 -->
    <BGHead class="flex-none" />

    <!-- 路由页面 -->
    <el-container class="flex-auto gap-[45px] max-h-[100vh_-_146.5px] overflow-y-auto">
      <bf-data-nav class="w-[292px]" />

      <el-main class="page-container !pr-[38px] !pb-[47px] !p-0 !flex flex-col gap-[5px]">
        <page-header>
          <div v-if="childRoutes.length > 0" :key="route.matched[1]?.name" class="flex items-center gap-4 z-[100] ml-[50px]">
            <el-tag
              v-for="child in childRoutes"
              :key="child.name"
              class="!border-none text-white text-[14px] font-medium cursor-pointer"
              :style="getTagStyle(child)"
              @click="handleChildRouteClick(child)"
            >
              {{ getChildRoute<PERSON>abel(child) }}
            </el-tag>
          </div>
        </page-header>

        <router-view v-slot="{ Component }">
          <!-- 使用 keep-alive 缓存路由组件，确保组件状态保持 -->
          <keep-alive>
            <component :is="Component" class="w-full" />
          </keep-alive>
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
  import '@/modules/dataManager'
  import BGHead from '@/layouts/BFHead.vue'
  import BfDataNav from '@/layouts/BfDataNav.vue'
  import PageHeader from '@/layouts/PageHeader.vue'
  import bfprocess from '@/utils/bfprocess.js'
  import { useRoute, useRouter } from 'vue-router'
  import { computed } from 'vue'
  import { useI18n } from 'vue-i18n'
  import authorization from '@/platform/dataManage/otherManage/authorization.vue'
  import systemVersion from '@/platform/dataManage/otherManage/systemVersion.vue'
  import openDialog from '@/utils/dialog'
  import { DataManageRouteName } from '@/router'

  const route = useRoute()
  const router = useRouter()
  const { t } = useI18n()
  window.bfglob.currentPlatform = DataManageRouteName
  bfprocess.loginedAfterFunc()

  // 获取当前路由的子路由
  const childRoutes = computed(() => {
    // 获取当前二级路由名称（如 controllerManage, deviceManage 等）
    const currentParentRoute = route.matched[1]?.name
    if (!currentParentRoute) return []

    // 从路由配置中获取子路由
    const parentRouteConfig = router
      .getRoutes()
      .find(r => r.path === '/dataManage')
      ?.children?.find(child => child.name === currentParentRoute)
    console.log('parentRouteConfig?.children ', parentRouteConfig?.children)
    return parentRouteConfig?.children || []
  })

  // 子路由名称映射
  const childRouteLabels = computed(() => ({
    // 设备管理子路由
    Controllers: t('nav.ctrlData'),
    repeaterWriteFrequency: t('nav.repeaterWriteFrequency'),
    ctrlonlineHistory: t('nav.controllerHistoryEvent'),

    // 终端管理子路由
    Devices: t('dialog.deviceDataTitle'),
    interphoneWf: t('nav.interphoneWriteFrequency'),
    onlineHistory: t('nav.switchHistory'),

    // 电话管理子路由
    GatewayFilter: t('nav.phoneBlackWhiteList'),
    GatewayPermission: t('nav.phoneDeviceAuth'),
    PredefinedPhoneBook: t('nav.predefinedPhoneBook'),
    ShortNumberMapping: t('nav.phoneGatewayMapping'),

    // 巡逻管理子路由
    LinePoint: t('nav.patrolPointManage'),
    Lines: t('nav.patrolRouteManage'),
    Rules: t('nav.patrolRuleManage'),

    // 其他操作子路由
    MapPoints: t('nav.mapPointData'),
    DynamicGroup: t('dynamicGroup.title'),
    authorization: t('nav.authorization'),
    relatedSoftware: t('nav.relatedSoftware'),
    systemVersion: t('nav.version'),
    notes: t('nav.runNotes'),
    crudHistory: t('nav.crudHistory'),
  }))

  // 获取子路由的显示标签
  const getChildRouteLabel = child => {
    return childRouteLabels.value[child.name] || child.name
  }

  // 获取标签样式
  const getTagStyle = child => {
    const isActive = route.name === child.name

    if (isActive) {
      // 激活状态的橙色渐变
      return 'background: linear-gradient(90deg, rgba(253, 161, 19, 0.0001) 0%, #fda215 50.16%, rgba(254, 159, 15, 0.0001) 100%)'
    } else {
      // 非激活状态的蓝色渐变
      return 'background: linear-gradient(90deg, rgba(20, 186, 255, 0.00006) 0%, rgba(20, 186, 255, 0.6) 50.16%, rgba(20, 186, 255, 0.00006) 100%)'
    }
  }

  // 组件映射表
  const componentMap = {
    authorization: authorization,
    systemVersion: systemVersion,
  }

  // 弹窗标题映射表
  const dialogTitleMap = {
    authorization: 'nav.authorization',
    systemVersion: 'nav.version',
  }

  /**
   * 通用弹窗打开函数
   * @param {string} componentName - 组件名称
   * @param {Object} props - 传递给组件的属性
   * @returns {Promise} 返回Promise，可以获取弹窗关闭时的结果
   */
  function openComponentDialog(componentName, props = {}) {
    const component = componentMap[componentName]
    const title = t(dialogTitleMap[componentName] || 'dialog.title')
    return openDialog(component, { ...props, title })
  }

  // 处理子路由点击
  const handleChildRouteClick = child => {
    // 检查是否为不需要跳转的路由
    if (child.meta?.unJumpRoute) {
      // 打开对应的弹窗组件
      openComponentDialog(child.name)
      return
    }

    // 正常路由跳转逻辑
    const currentParentRoute = route.matched[1]?.name
    if (!currentParentRoute) return

    const targetPath = `/dataManage/${currentParentRoute}/${child.name}`
    router.push({
      path: targetPath,
      query: route.query, // 保持查询参数
    })
  }
</script>

<style scoped lang="scss"></style>
