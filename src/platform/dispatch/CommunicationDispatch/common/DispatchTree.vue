<template>
  <div class="dispatch-tree-wrapper">
    <VxeTableTree :withPageHeader="true" :filter="true" :menuConfig="menuConfig" :menuEventHandler="menuEventHandler" />
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue'
  import { useI18n } from 'vue-i18n'
  import { DeviceTypes } from '@/utils/bfutil'
  import { MenuConfig, MenuEventHandler, TreeNodeType } from '@/components/common/tableTree'
  import openDialog from '@/utils/dialog'
  import BfSpeaking from '@/components/command/bfSpeaking.vue'

  // 支持查看设备状态的终端类型
  const SupportStatusDeviceTypes = [
    DeviceTypes.Device,
    DeviceTypes.Mobile,
    DeviceTypes.VirtualClusterDevice,
    DeviceTypes.PocDevice,
    DeviceTypes.VirtualRepeater,
  ]

  const statusContextMenuCode = ['stats', 'cb01', 'cb02', 'cb09']

  const { t } = useI18n()

  const menuConfig = computed<MenuConfig>(() => {
    return {
      body: {
        options: [
          [
            {
              name: t('tree.quickCall'),
              code: 'quickCall',
            },
            {
              disabled: true,
              visible: false,
              name: t('dialog.locateCtrl'),
              code: 'cb01',
            },
            {
              disabled: true,
              visible: false,
              name: t('dialog.trailCtrl'),
              code: 'cb02',
            },
            {
              disabled: true,
              visible: false,
              name: t('dialog.telecontrol'),
              code: 'cb09',
            },
            {
              disabled: true,
              visible: false,
              name: t('tree.status'),
              code: 'stats',
            },
          ],
          [
            {
              name: t('tree.collapseAll'),
              code: 'collapseAll',
            },
            {
              name: t('tree.expandAll'),
              code: 'expandAll',
            },
            {
              name: t('tree.online'),
              code: 'displayOnline',
            },
            {
              name: t('tree.displayAllDev'),
              code: 'displayAll',
            },
          ],
        ],
      },
      visibleMethod: ({ options, row }) => {
        let hasStatus = false

        if (row.nodeType === TreeNodeType.Terminal) {
          const device = bfglob.gdevices.get(row.rid)
          // 只有指定的设备才显示状态
          if (device && SupportStatusDeviceTypes.includes(device.deviceType)) {
            hasStatus = true
          }
        }

        const visible = row.nodeType === TreeNodeType.Org ? false : hasStatus
        const disabled = !visible

        options?.forEach(list => {
          list.forEach(item => {
            if (statusContextMenuCode.includes(item.code)) {
              item.visible = visible
              item.disabled = disabled
            }
          })
        })

        return true
      },
    } satisfies MenuConfig
  })

  const menuEventHandler: MenuEventHandler = ({ menu, row }) => {
    // todo: handle click
    switch (menu.code) {
      case 'quickCall':
        let dmrId = ''
        if (row.nodeType === TreeNodeType.Terminal) {
          const data = bfglob.gdevices.get(row.rid)
          dmrId = data?.dmrId ?? ''
        } else {
          const data = bfglob.gorgData.get(row.rid)
          dmrId = data?.dmrId ?? ''
        }
        openDialog(BfSpeaking).then(vm => {
          ;(vm.value as InstanceType<typeof BfSpeaking>).speakFast(dmrId)
        })
        break
      case 'cb01':
        // 处理 cb01
        break
      case 'cb02':
        // 处理 cb02
        break
      case 'cb09':
        // 处理 cb09
        break
      case 'stats':
        // 处理状态
        break
    }
  }
</script>

<style lang="scss">
  .dispatch-tree-wrapper {
    height: 100%;
    width: 100%;
  }
</style>
